// Grafana - jedd experimentation

// 2024-03 - preparation for:
//     a)  expiration of 'grafana.mtm' - migration of TEST grafana-obs instance
//     b)  expiration of on-prem enterprise licence (2024-04)

// Uses /opt/sharednfs/grafana-jedd for persistent storage.  

# @TODO sort out https certs problems - might need copy of certs locally

# @TODO sso - almost definitely never achievable

# @TODO postgresql task - the grafana task should wait for this to be running, 
# otherwise we have at least one failed grafana allocation at each job-start as
# it gets ahead of postgresql readiness

# @TODO sanitise residual configuration (environment) variables below - put as 
# many into the assets/grafana-jedd.ini file as possible

# @TODO work out whether WARN about /usr/share/grafana/plugins-bundled should 
# be / can be suppressed or resolved - it's related to one of the PATHs we can 
# define, but probably should NOT be redefined from default (internal to container)
# as the variable defines bin/grafana and other key paths - but the docker container 
# does NOT ship with an empty plugins-bundled directory for some reason. Perhaps a 
# pre-start mkdir would suppress safely


# Requirements = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# CONSUL variables must exist:
#     grafana-jedd/POSTGRES_DB
#     grafana-jedd/POSTGRES_USER
#     grafana-jedd/POSTGRES_PASSWORD


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  # image_grafana = "quay.education.nsw.gov.au/observability/grafana-enterprise:test-obscol"
  # image_grafana = "quay.education.nsw.gov.au/observability/grafana-enterprise:10.1.5"
  # 2023-08-24 OBS-676 jedd - moving from 12.15-1.pgdg120+1 (latest at previous instantiation) to 12.17 specifically

  # We are matching prod grafana during initial build as we will try to replicate
  # postgresql and other configuration data - maybe bringing across existing SSO
  # accounts - and can THEN upgrade in place to a more modern version of grafana.
  # Grafana prod mtm 2024-03-15 is ******* - but that's not available on docker hub
  image_grafana = "grafana/grafana:9.3.2"
  # image_grafana = "grafana/grafana:10.1.10"
  image_postgresql =  "postgres:12.17"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "grafana-jedd-grafana" {
  datacenters = ["dc-cir-un-prod"]

  # While testing, speed-up iterations by constraining to single host
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0475obscol0[6]"
  }

  type = "service"

  group "grafana-jedd" {

    network {
      port "port_grafana" {
        to = 3000
      }

      port "port_postgresql"  {
        to = 5432
      }
    }

    # Volumes are handled by explicit path to obt/sharednfs/ (refer below)


    # task grafana  = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "grafana" {
      driver = "docker"

      kill_signal = "SIGTERM"      

      # This sidecar approach should force Grafana to 'post start' after PostgreSQL - though
      # in practice Grafana should start fine without PostgreSQL answering immediately, and
      # just retry until the DB is ready.
      #lifecycle {
      #  hook = "poststart"
      #  sidecar = true
      #}

      # user = "root:root"

      config {
        image = "${var.image_grafana}"

        privileged = true

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        ports = ["port_grafana"]

        volumes = [

          # 2023-10-23 jedd - using THIS instead of relying on the GF_DATA ... paths, as those really
          #   don't seem to be respected.  This approach maintains plugins and other data between restarts.
          "/opt/sharednfs/grafana-jedd/grafana/var-lib-grafana:/var/lib/grafana",
          "/opt/sharednfs/grafana-jedd/:/persistent/",

          "local/grafana.ini:/etc/grafana/grafana.ini",
          "local/ldap.toml:/etc/grafana/ldap.toml",
          # @TODO copy prod license.jwt to /opt/sharednfs/grafana-jedd/grafana/license.jwt
          # "local/license.jwt:/persistent/grafana/license.jwt",
          "local/EXT_PROD_CHAIN.crt:/etc/pki/CA/certs/EXT_PROD_CHAIN.crt",
        ]

      }

#==      env {
#==        # Expose Grafana on this port - as with other ObsObs jobs we're sticking with standard ports for each job.
#==        GF_SERVER_HTTP_PORT   = "${NOMAD_PORT_port_grafana}"
#==
#==        # This points to /grafana is our persistent mount point - this directory defaults to /var/lib/grafana, 
#==        # and would contain several sub-directories: alerting/, csv/, plugins/, and png/
#==        # as well as the default location for the sqlite database file:   grafana.db
#==        # Note - plugins/ does NOT get created here at instantiation - but is at /var/lib/grafana/plugins internally - needs more experimentation
#==
#==        # 2023-10-23 jedd - removing this and forcing the volume overlay (above) directly to /var/lib/grafana
#==        # GF_PATHS_DATA         = "/persistent/grafana/DATA"
#==
#==        # Usually points to /usr/share/grafana - contains plugins-bundled and other distro / shipped files - we may want more control over this.
#==        # DISABLING - as this is about 300MB on grafana.mtm and contains lots of things we actually don't want to replicate .. probably
#==        # GF_PATHS_HOME         = "/persistent/grafana/usr-share"
#==
#==        # This defaults to /var/log/grafana - we may have better ways of extracting logs via nomad (docker/loki) but
#==        # for troubleshooting it should be convenient. Frustratingly Grafana employees like to pluralise everythings.
#==        GF_PATHS_LOGS         = "/grafana/log"
#==
#==        # GF_LOG_LEVEL “debug”, “info”, “warn”, “error”, and “critical”. Default is info
#==        # GF_LOG_LEVEL = "warn"
#==        GF_LOG_LEVEL = "debug"
#==
#==        # We can send logs to console (captured by loki above), or file (dumped to GF_PATHS_LOGS above), or both with "console file"
#==        GF_LOG_MODE           = "console"
#==
#==        # We probably want this set so we can call in dashboards from grafana.com
#==        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
#==        HTTP_PROXY  = "http://proxy.det.nsw.edu.au:80"
#==        NO_PROXY    = "10.0.0.0/8,172.0.0.0/8,192.168/16.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.consul"
#==
#==        # Pass the plugins you want installed to Docker with the GF_INSTALL_PLUGINS environment variable as a 
#==        # comma-separated list. This sends each plugin name to grafana-cli plugins install ${plugin} and installs 
#==        # them when Grafana starts.
#==        # GF_INSTALL_PLUGINS = "redis-datasource"
#==
#==        # We can load this up via the template (below) to autogenerate two datasources, though we prefer to keep
#==        # those in the SQLite database in GF_PATHS_DATA (/opt/grafana in host) so we can modify them, and more
#==        # conveniently add new items.  Commented out (also the template below) and create from within the UI.
#==        # GF_PATHS_PROVISIONING = "/local/grafana/provisioning"
#==
#==        # I can't even find a reference to this on the googles in 2022-11
#==        # GF_LIVE_ALLOWED_ORIGINS = "http://*"
#==
#==        # This is another poorly documented feature, and I'm doubtful that we need it.
#==        # GF_FEATURE_TOGGLES_ENABLE = "ngalert"
#==
#==        # Database configuration - should probably not be in grafana.ini as this way we can utilise
#==        # the same credentials and secrets from Consul that are then used by the postgresql-backup task below.
#==        GF_DATABASE_TYPE = "postgres"
#==
#==        # original grafana.mtm credentials
#==        #GF_DATABASE_NAME = "grafana_db_admin"
#==        #GF_DATABASE_USER = "grafanadb_p"
#==        #GR_DATABASE_PASSWORD = S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU
#==        GF_DATABASE_NAME = "grafana"
#==        GF_DATABASE_USER = "grafana"
#==        GF_DATABASE_PASSWORD = "S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU"
#==
#==
#==        #GF_DATABASE_HOST = "${NOMAD_ADDR_port_postgresql}"
#==        # GF_DATABASE_NAME = {{ key "grafana-jedd/POSTGRES_DB" }}
#==        # GF_DATABASE_USER = {{ key "grafana-jedd/POSTGRES_USER" }}
#==        # GF_DATABASE_PASSWORD = {{ key "grafana-jedd/POSTGRES_PASSWORD" }}
#==
#==        #GF_DATABASE_NAME = "grafana"
#==        #GF_DATABASE_USER = "grafana"
#==        #GF_DATABASE_PASSWORD = "password"
#==
#==        # With Postgresql can be: "require" (default), "verify-full", "verify-ca", and "disable"
#==        #GF_DATABASE_SSL_MODE = "disable"
#==
#==        # GF_DATABASE_LOG_QUERIES = "true"
#==
#==        # GF_SERVER_ROOT_URL = "http://grafana.obs.nsw.education"
#==
#==        GF_ALERTING_ENABLED = "false"
#==        GF_UNIFIED_ALERTING_ENABLED = "false"
#==
#==        GF_DEFAULT_FORCE_MIGRATION  = "false"
#==      }
#==



      service {
        name = "grafana"
        port = "port_grafana"

#        check {
#          type = "http"
#          port = "port_grafana"
#          path = "/api/health"
#          interval = "5s"
#          timeout = "2s"
#        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana.entrypoints=http,https",
          "traefik.http.routers.grafana.tls=false",
          "traefik.http.routers.grafana.rule=Host(`grafana.obs.nsw.education`)",
        ]      
      }

      resources {
        cpu    = 700
        memory = 3000
      }

#### Disabling this entirely as we will create the data sources and persist them in the /grafana
#### storage mapped above onto host's /opt/grafana structure.  

# @TODO explore failure states when provisioning extant datasource, and/or provision fails (remote
# end not available) for single source, and/or editing of an existing datasource originally auto-
# provisioned, etc.  Persistence is where we are coming from, and may better suit operational and
# upgrade processes.

      template {
        # We have a modified version of the original grafana.mtm. (pu0992tagf0001)
        # grafana.ini that we're starting with.  
        data = file("./assets/grafana-jedd.ini")
        destination = "local/grafana.ini"
      }

      template {
        # Not 100% sure this is actually used - no documentation was made of the original
        # grafana configuration / build.
        data = file("./assets/grafana-jedd.ldap.toml")
        destination = "local/ldap.toml"
      }

      template {
        data = file("./assets/grafana-jedd.ext_prod_chain.crt")
        destination = "local/EXT_PROD_CHAIN.crt"
      }

#      template {
#        # Licence license file is keyed to fqdn hostname and uses american spelling of the noun.
#        # Because DATA is pointing to persistent/grafana/DATA - we can keep license key on sharednfs, probably
#        data = file("./assets/grafana-jedd.license.jwt")
#        destination = "local/license.jwt"
#      }

    } // end-task grafana


  }
}

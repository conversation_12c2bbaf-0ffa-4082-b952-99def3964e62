job "grafana" {
  type = "service"
  datacenters = ["dc1"]

  group "grafana" {

    task "grafana" {
      driver = "docker"

      config {
        image = "https://docker.io/grafana/grafana:latest"
        dns_servers = ["192.168.31.1"]
        port_map {
          http = 3000
        }

        volumes = [
          "local/datasources.yml://usr/share/grafana/conf/provisioning/datasources/datasources.yml",
        ]
      }

      // /usr/share/grafana/conf/provisioning/datasources

      resources {
        network {
          port "http" {
            static = 3000
          }
        }
      }

      service {
        name = "grafana"
        port = "http"
      }

      env {

      }

      template {
        data = <<EOH
apiVersion: 1

datasources:
  - name: Elasticsearch
    type: elasticsearch
    orgId: 1
    access: proxy
    url: https://vpc-obs-openplatform-poc-g2pxfsabnwkcu5utj4kkpvzmlq.ap-southeast-2.es.amazonaws.com:443
    basicAuth: true
    basicAuthUser: superuser
    basicAuthPassword:
    database: '[metricbeat-7.9.3-]YYYY.MM.DD'
    isDefault: true
    readOnly: false
    withCredentials: false
    version: 1
    jsonData:
      esVersion: 70
      interval: Daily
      logLevelField:
      logMessageField:
      maxConcurrentShardRequests: 5
      timeField: @timestamp
    secureJsonFields:
      basicAuthPassword: '9QrRJvp7r/les7SLh79ZfYT9+bRgc6gEv5ykM8gNmz4='
EOH
        destination = "local/datasources.yml"
      }
    }
  }
}

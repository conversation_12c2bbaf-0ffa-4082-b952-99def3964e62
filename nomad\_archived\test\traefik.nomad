// traefik LB load balancer - for ObsCol (test) nomad - system job (all nodes)
job "traefik" {
  region      = "global"
  datacenters = ["dc-un-test"]
  type        = "system"

  group "traefik" {

    network {
      dns {
        servers = ["192.168.31.1"]
      }

      port "http" {
        static = 80
      }

      port "https" {
        static = 443
      }

      port "traefik" {
        static = 8081
      }
    }

    service {
      name = "traefik"

      check {
        name     = "alive"
        type     = "tcp"
        port     = "http"
        interval = "10s"
        timeout  = "2s"
      }
    }

    task "traefik" {
      driver = "docker"

      config {
        image        = "https://docker.io/library/traefik:v2.4"
        network_mode = "host"

        volumes = [
          "local/traefik.toml:/etc/traefik/traefik.toml",
        ]
      }

      template {
        data = <<EOF
debug = false
logLevel = "INFO"
defaultEntryPoints = ["https","http"]

[entryPoints]
    [entryPoints.http]
    address = ":80"
    [entryPoints.https]
    address = ":443"
      [entryPoints.https.http.tls]
        [[entryPoints.https.http.tls.domains]]
        main = "obscol.obs.test.nsw.education"
        sans = ["*.obs.test.nsw.education"]

    [entryPoints.traefik]
    address = ":8081"

    [entryPoints.prometheus]
    address = ":9090"

    [entryPoints.cortex]
    address = ":9095"

    [entryPoints.jaegergrpc]
    address = ":14250"

    [entryPoints.nodered]
    address = ":1880"

[api]
    dashboard = true
    insecure  = true



# Enable Consul Catalog configuration backend.
[providers.consulCatalog]
    prefix           = "traefik"
    exposedByDefault = false

    [providers.consulCatalog.endpoint]
      address = "consul.service.dc-cir-un-test.collectors.obs.test.nsw.education:8500"
      scheme  = "http"

# Enable File Provider for "Dynamic Configuration" elements
[providers.file]
  directory = "/local/traefik.d"

# 2021-09-09 jedd - enable metrics out of traefik
[metrics]
  [metrics.prometheus]
    buckets = [0.1, 0.3, 1.2, 5.0]

EOF

        destination = "local/traefik.toml"
      }

      template {
        destination = "local/traefik.d/certificates.toml"
        data = <<EOF
[tls.stores]
  [tls.stores.default]
    # This one should be used if the client did not perform an SNI handshake.
    [tls.stores.default.defaultCertificate]
      certFile = "/local/obscol.obs.test.nsw.education.pem"
      keyFile = "/local/obscol.obs.test.nsw.education.key"

[[tls.certificates]]
  certFile = "/local/obscol.obs.test.nsw.education.pem"
  keyFile = "/local/obscol.obs.test.nsw.education.key"

EOF
      }

      template {
        destination = "local/obscol.obs.test.nsw.education.pem"
        data = <<EOF
-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIETjCCAzagAwIBAgINAe5fIh38YjvUMzqFVzANBgkqhkiG9w0BAQsFADBMMSAw
HgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEGA1UEChMKR2xvYmFs
U2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjAeFw0xODExMjEwMDAwMDBaFw0yODEx
MjEwMDAwMDBaMFAxCzAJBgNVBAYTAkJFMRkwFwYDVQQKExBHbG9iYWxTaWduIG52
LXNhMSYwJAYDVQQDEx1HbG9iYWxTaWduIFJTQSBPViBTU0wgQ0EgMjAxODCCASIw
DQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKdaydUMGCEAI9WXD+uu3Vxoa2uP
UGATeoHLl+6OimGUSyZ59gSnKvuk2la77qCk8HuKf1UfR5NhDW5xUTolJAgvjOH3
idaSz6+zpz8w7bXfIa7+9UQX/dhj2S/TgVprX9NHsKzyqzskeU8fxy7quRU6fBhM
abO1IFkJXinDY+YuRluqlJBJDrnw9UqhCS98NE3QvADFBlV5Bs6i0BDxSEPouVq1
lVW9MdIbPYa+oewNEtssmSStR8JvA+Z6cLVwzM0nLKWMjsIYPJLJLnNvBhBWk0Cq
o8VS++XFBdZpaFwGue5RieGKDkFNm5KQConpFmvv73W+eka440eKHRwup08CAwEA
AaOCASkwggElMA4GA1UdDwEB/wQEAwIBhjASBgNVHRMBAf8ECDAGAQH/AgEAMB0G
A1UdDgQWBBT473/yzXhnqN5vjySNiPGHAwKz6zAfBgNVHSMEGDAWgBSP8Et/qC5F
JK5NUPpjmove4t0bvDA+BggrBgEFBQcBAQQyMDAwLgYIKwYBBQUHMAGGImh0dHA6
Ly9vY3NwMi5nbG9iYWxzaWduLmNvbS9yb290cjMwNgYDVR0fBC8wLTAroCmgJ4Yl
aHR0cDovL2NybC5nbG9iYWxzaWduLmNvbS9yb290LXIzLmNybDBHBgNVHSAEQDA+
MDwGBFUdIAAwNDAyBggrBgEFBQcCARYmaHR0cHM6Ly93d3cuZ2xvYmFsc2lnbi5j
b20vcmVwb3NpdG9yeS8wDQYJKoZIhvcNAQELBQADggEBAJmQyC1fQorUC2bbmANz
EdSIhlIoU4r7rd/9c446ZwTbw1MUcBQJfMPg+NccmBqixD7b6QDjynCy8SIwIVbb
0615XoFYC20UgDX1b10d65pHBf9ZjQCxQNqQmJYaumxtf4z1s4DfjGRzNpZ5eWl0
6r/4ngGPoJVpjemEuunl1Ig423g7mNA2eymw0lIYkN5SQwCuaifIFJ6GlazhgDEw
fpolu4usBCOmmQDo8dIm7A9+O4orkjgTHY+GzYZSR+Y0fFukAj6KYXwidlNalFMz
hriSqHKvoflShx8xpfywgVcvzfTO3PYkz6fiNJBonf6q8amaEsybwMbDqKWwIX7eSPY=
-----END CERTIFICATE-----
-----BEGIN CERTIFICATE-----
MIIDXzCCAkegAwIBAgILBAAAAAABIVhTCKIwDQYJKoZIhvcNAQELBQAwTDEgMB4G
A1UECxMXR2xvYmFsU2lnbiBSb290IENBIC0gUjMxEzARBgNVBAoTCkdsb2JhbFNp
Z24xEzARBgNVBAMTCkdsb2JhbFNpZ24wHhcNMDkwMzE4MTAwMDAwWhcNMjkwMzE4
MTAwMDAwWjBMMSAwHgYDVQQLExdHbG9iYWxTaWduIFJvb3QgQ0EgLSBSMzETMBEG
A1UEChMKR2xvYmFsU2lnbjETMBEGA1UEAxMKR2xvYmFsU2lnbjCCASIwDQYJKoZI
hvcNAQEBBQADggEPADCCAQoCggEBAMwldpB5BngiFvXAg7aEyiie/QV2EcWtiHL8
RgJDx7KKnQRfJMsuS+FggkbhUqsMgUdwbN1k0ev1LKMPgj0MK66X17YUhhB5uzsT
gHeMCOFJ0mpiLx9e+pZo34knlTifBtc+ycsmWQ1z3rDI6SYOgxXG71uL0gRgykmm
KPZpO/bLyCiR5Z2KYVc3rHQU3HTgOu5yLy6c+9C7v/U9AOEGM+iCK65TpjoWc4zd
QQ4gOsC0p6Hpsk+QLjJg6VfLuQSSaGjlOCZgdbKfd/+RFO+uIEn8rUAVSNECMWEZ
XriX7613t2Saer9fwRPvm2L7DWzgVGkWqQPabumDk3F2xmmFghcCAwEAAaNCMEAw
DgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFI/wS3+o
LkUkrk1Q+mOai97i3Ru8MA0GCSqGSIb3DQEBCwUAA4IBAQBLQNvAUKr+yAzv95ZU
RUm7lgAJQayzE4aGKAczymvmdLm6AC2upArT9fHxD4q/c2dKg8dEe3jgr25sbwMp
jjM5RcOO5LlXbKr8EpbsU8Yt5CRsuZRj+9xTaGdWPoO4zzUhw8lo/s7awlOqzJCK
6fBdRoyV3XpYKBovHd7NADdBj+1EbddTKJd+82cEHhXXipa0095MJ6RMG3NzdvQX
mcIfeg7jLQitChws/zyrVQ4PkX4268NXSb7hLi18YIvDQVETI53O9zJrlAGomecs
Mx86OyXShkDOOyyGeMlhLxS67ttVb9+E7gUJTb0o2HLO02JQZR7rkpeDMdmztcpH
WD9f
-----END CERTIFICATE-----
EOF
      }

      template {
        destination = "local/obscol.obs.test.nsw.education.key"
        data = <<EOF
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************EOF
      }

      resources {
        cpu    = 100
        memory = 128
      }
    }
  }
}


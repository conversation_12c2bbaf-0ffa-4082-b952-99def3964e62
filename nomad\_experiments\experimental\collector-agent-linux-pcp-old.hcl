job "collector-agent-linux-pcp" {
  datacenters = ["dev"]
  type = "service"

  group "collector" {
    count = 1

    task "zabbix-proxy" {
      driver = "docker"

      config {
        image = "dl0992tbld0001.nsw.education/zabbix-proxy-postgresql:centos-4.0.6"
        hostname = "collector-agent-linux-pcp.mtm.dev.det.nsw.edu.au"
        command = "su zabbix -s /bin/bash -c '/usr/sbin/zabbix_proxy --foreground -c /etc/zabbix/zabbix_proxy.conf'"

        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }

//        volumes = [
//          "local/zabbix_proxy.conf:/etc/zabbix/zabbix_proxy.conf"
//        ]
      }

      service {
        name = "collector-agent-linux-pcp"
        port = "zabbix_passive"
        tags = [
          "collector",
          "collector-linux"]
      }

//      template {
//        source = "/opt/nomad/templates/zabbix_proxy.conf.ctmpl"
//        destination = "local/zabbix_proxy.conf"
//        change_mode = "restart"
//      }

      env {
        # collector-agent-linux-pcp.mtm.dev.det.nsw.edu.au.yml
        # expressed in the same order as the recipe file.

        # dns_name
        "ZBX_HOSTNAME" = "collector-agent-linux-pcp.mtm.dev.det.nsw.edu.au"

        # zabbix_server
        "ZBX_SERVER_HOST" = "du0992tedbd001.hbm.det.nsw.edu.au"

        # zabbix_proxy_startjavapollers: 8
        "ZBX_STARTJAVAPOLLERS" = "8"
        # zabbix_proxy_javagateway: 127.0.0.1
        "ZBX_JAVAGATEWAY" = "zabbix-java-gateway"
        # zabbix_proxy_javagatewayport: 13053
        "ZBX_JAVAGATEWAYPORT" = "13053"

        # zabbix_proxy_offlinebuffer: 72
        "ZBX_PROXYOFFLINEBUFFER" = "72"
        # zabbix_proxy_configfrequency: 600
        "ZBX_CONFIGFREQUENCY" = "600"
        # zabbix_proxy_startpollers: 32
        "ZBX_STARTPOLLERS" = "32"
        # zabbix_proxy_startipmipollers: 0
        # "ZBX_IPMIPOLLERS" = "0"
        # zabbix_proxy_startpollersunreachable: 16
        "ZBX_STARTPOLLERSUNREACHABLE" = "16"
        # zabbix_proxy_startpingers: 16
        "ZBX_STARTPINGERS" = "16"
        # zabbix_proxy_startdiscoverers: 8
        "ZBX_STARTDISCOVERERS" = "8"
        # zabbix_proxy_starthttppollers: 1
        # "ZBX_STARTHTTPPOLLERS" = "1"
        # zabbix_proxy_startvmwarecollector: 0
        # "ZBX_STARTVMWARECOLLECTORS" = "0"
        ## Proxy memory and perf settings
        # zabbix_proxy_cachesize: 256
        "ZBX_CACHESIZE" = "256M"
        # zabbix_proxy_historycachesize: 128
        "ZBX_HISTORYCACHESIZE" = "128M"
        # zabbix_proxy_historyindexcachesize: 32
        "ZBX_HISTORYINDEXCACHESIZE" = "32M"
        # Unreachability settings
        # zabbix_proxy_unreachableperiod: 120
        "ZBX_UNREACHABLEPERIOD" = "120"
        # zabbix_proxy_unreachabedelay: 30
        "ZBX_UNAVAILABLEDELAY" = "30"

        "DB_SERVER_HOST" = "${NOMAD_IP_db_postgresql}"
        "DB_SERVER_PORT" = "${NOMAD_PORT_db_postgresql}"
      }

      resources {
        network {
          port "zabbix_passive" {}
          port "zabbix_server" {
            static = 10052
          }
        }
      }
    }

    task "db" {
      driver = "docker"

      config {
        image = "dl0992tbld0001.nsw.education/postgres:9.6"
        hostname = "collector-agent-linux-pcp-db.mtm.dev.det.nsw.edu.au"

        port_map {
          postgresql = 5432
        }

        volumes = [
          "/opt/nomad/database/postgresql_4.0.6/schema.sql:/docker-entrypoint-initdb.d/schema.sql"
        ]
      }

      service {
        name = "collector-agent-linux-pcp-db"
        port = "postgresql"
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

      resources {
        network {
          port "postgresql" {}
        }
      }
    }
  }
}
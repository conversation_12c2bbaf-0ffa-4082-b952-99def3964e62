// prod - obscol - nomad - hcl v1 - prometheus + metricbeat

job "prometheus-with-metricbeat" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "prometheus" {

    network {
      port "prometheus" {
        static = 9090
      }
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pl0992obscol01.nsw.education"
      }

    task "prometheus" {
      driver = "docker"

      config {
        image = "https://docker.io/prom/prometheus:v2.27.1"
        dns_servers = ["************"]
        port_map {
          prometheus = 9090
        }
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
      }

      service {
        name = "prometheus-http"
        port = "prometheus"

        check {
          type = "http"
          port = "prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']



# NIFI test with different URL path

  - job_name: 'pl0991nifi1001.nsw.education'
    metrics_path: /metrics/
    static_configs:
      - targets: ['**********:19401']
        labels:
          cir_app_id: nifi
          env: prod

  - job_name: 'pl0991nifi1002.nsw.education'
    metrics_path: /metrics/
    static_configs:
      - targets: ['**********:19401']
        labels:
          cir_app_id: nifi
          env: prod

  - job_name: 'pl0991nifi1003.nsw.education'
    metrics_path: /metrics/
    static_configs:
      - targets: ['**********:19401']
        labels:
          cir_app_id: nifi
          env: prod

  - job_name: 'pl0991nifi1004.nsw.education'
    metrics_path: /metrics/
    static_configs:
      - targets: ['**********:19401']
        labels:
          cir_app_id: nifi
          env: prod

  - job_name: 'pl0991nifi1005.nsw.education'
    metrics_path: /metrics/
    static_configs:
      - targets: ['**********:19401']
        labels:
          cir_app_id: nifi
          env: prod

  - job_name: 'pl0991nifi1006.nsw.education'
    metrics_path: /metrics/
    static_configs:
      - targets: ['10.22.1.89:19401']
        labels:
          cir_app_id: nifi
          env: prod

  - job_name: 'pl0991nifi1007.nsw.education'
    metrics_path: /metrics/
    static_configs:
      - targets: ['10.22.1.88:19401']
        labels:
          cir_app_id: nifi
          env: prod

  - job_name: 'pl0991nifi1008.nsw.education'
    metrics_path: /metrics/
    static_configs:
      - targets: ['10.22.1.87:19401']
        labels:
          cir_app_id: nifi
          env: prod


  - job_name: 'pl0991nifi1001.nsw.education.FQDN'
    metrics_path: /metrics/
    static_configs:
      - targets: ['pl0991nifi1001.nsw.education:19401']
        labels:
          cir_app_id: nifi
          env: prod




# Obs stuff

  - job_name: 'pu0992ttdbd001.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992ttdbd001.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: zabbix_core

  - job_name: 'pu0991tedbd001.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0991tedbd001.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: zabbix_core

  - job_name: 'pu0992ttdbs001.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992ttdbs001.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: zabbix_edge

  - job_name: 'pu0991tedbs001.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0991tedbs001.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: zabbix_edge

  - job_name: 'pu0992tcdnd001.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992tcdnd001.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: nomad

  - job_name: 'pu0992tcdnd002.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992tcdnd002.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: nomad

  - job_name: 'pu0992tcdnd003.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992tcdnd003.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: nomad

  - job_name: 'pu0992tcdnd004.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992tcdnd004.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: nomad

  - job_name: 'pu0992tcdnd005.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992tcdnd005.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: nomad

  - job_name: 'pu0992tcdnd006.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992tcdnd006.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: nomad

  - job_name: 'pu0992tcdnd007.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992tcdnd007.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: nomad

  - job_name: 'pu0992tcdnd008.hbm.det.nsw.edu.au'
    static_configs:
      - targets: ['pu0992tcdnd008.hbm.det.nsw.edu.au:19407']
        labels:
          cir_app_id: obs
          env: prod
          role: nomad

# Kafka stuff

  - job_name: 'pl0991kfkad001.nsw.education'
    static_configs:
      - targets: ['pl0991kfkad001.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod
          role: ftp

  - job_name: 'pl0991kfkad002.nsw.education'
    static_configs:
      - targets: ['pl0991kfkad002.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod
          role: ftp



  - job_name: 'pl0991kfkab001.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab001.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

  - job_name: 'pl0991kfkab0002.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab0002.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

  - job_name: 'pl0991kfkab0003.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab0003.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

  - job_name: 'pl0991kfkac0001.nsw.education'
    static_configs:
      - targets: ['pl0991kfkac0001.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

  - job_name: 'pl0991kfkac0002.nsw.education'
    static_configs:
      - targets: ['pl0991kfkac0002.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

  - job_name: 'pl0991kfkac0003.nsw.education'
    static_configs:
      - targets: ['pl0991kfkac0003.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

  - job_name: 'pl0991kfkaz0001.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaz0001.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

  - job_name: 'pl0991kfkaz0002.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaz0002.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

  - job_name: 'pl0991kfkaz0003.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaz0003.nsw.education:19407']
        labels:
          cir_app_id: kfka
          env: prod

# === === === === === === === === === === === === === === === === === === === 


  - job_name: 'pl0991kfkab1001.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab1001.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkab1002.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab1002.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkab1003.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab1003.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkab1004.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab1004.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkab1005.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab1005.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkab1006.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab1006.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkab1007.nsw.education'
    static_configs:
      - targets: ['pl0991kfkab1007.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkac1001.nsw.education'
    static_configs:
      - targets: ['pl0991kfkac1001.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkac1002.nsw.education'
    static_configs:
      - targets: ['pl0991kfkac1002.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaq1001.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaq1001.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaq1002.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaq1002.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaq1003.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaq1003.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaq1004.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaq1004.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaq1005.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaq1005.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaq1006.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaq1006.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaq1007.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaq1007.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaq1008.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaq1008.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaz1001.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaz1001.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaz1002.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaz1002.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod

  - job_name: 'pl0991kfkaz1003.nsw.education'
    static_configs:
      - targets: ['pl0991kfkaz1003.nsw.education:19407']
        labels:
          cir_app_id: okfkaf
          env: prod


# === === === === === === === === === === === === === === === === === === === 


  - job_name: 'nomad'
    consul_sd_configs:
      - server: 'consul.service.dc-un-prod.consul:8500'
        datacenter: 'dc-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['nomad-client', 'nomad']


    relabel_configs:
      - source_labels: ['__meta_consul_tags']
        regex: '(.*)http(.*)'
        action: keep

    metrics_path: /v1/metrics
    params:
      format: ['prometheus']

remote_write:
  - url: "http://prometheus-remote-write.service.dc-cir-un-prod.collectors.obs.nsw.education:9201/write"
  # - url: "http://${NOMAD_IP_metricbeat_prometheus_remote_write}:9201/write"
  # - url: "http://*************:9201/write"
EOH
        destination = "local/prometheus.yaml"
      }
    }

    task "metricbeat" {
      driver = "docker"

      config {
        image = "https://docker.elastic.co/beats/metricbeat:7.12.0"

        # always use dnsmasq to cache outgoing queries
        dns_servers = ["************"]

        port_map {
          prometheus_remote_write = 9201
        }

        volumes = [
          "local/prometheus.yml:/usr/share/metricbeat/modules.d/prometheus.yml",
          "local/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml"
        ]
      }

      resources {
        network {
          port "prometheus_remote_write" {
            static = 9201
          }
        }
      }

      service {
        name = "prometheus-remote-write"
        port = "prometheus_remote_write"
      }

      env {
        ELASTICSEARCH_HOSTS = "https://metricbeat:<EMAIL>:9200"
      }

      template {
        data = <<EOH
metricbeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

processors:
  - add_docker_metadata: ~

output.elasticsearch:
#  hosts: '${ELASTICSEARCH_HOSTS}'
#  hosts: 'https://metricbeat:<EMAIL>:9200'
  hosts: '*******************************************************'

  # Need this as the ssl cert for obses elastic cluster is self-signed
  ssl.verification_mode: 'none'

setup.ilm.enabled: true

EOH
        destination = "local/metricbeat.yml"
      }

      template {
        data = <<EOH
- module: prometheus
  metricsets: ["remote_write"]
  host: "0.0.0.0"
  port: "9201"
EOH
        destination = "local/prometheus.yml"
      }
    }
  }
}

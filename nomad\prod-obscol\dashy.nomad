# dashy - quick-access dashboard - prod obscol

variables {
  image_dashy = "quay.education.nsw.gov.au/observability/dashy:prod-obscol"
}

job "dashy"  {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

#  update {
#    max_parallel      = 1
#    min_healthy_time  = "10s"
#    healthy_deadline  = "2m"
#    canary            = 1
#    auto_promote      = true
#    auto_revert       = true
#  }

// Constraint added as a temporary fix to the coexistance issues across the prod boxes
#  constraint {
#    attribute = "${attr.unique.hostname}"
#    operator = "regexp"
#    value = "pl0992obscol0[123]"
#  }
#
  group "dashy" {
    network {
      port "port_http" {
        to = 8080
      }
    }

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    task "dashy" {
      driver = "docker"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }

      config {
        image = var.image_dashy
        hostname = "dash"
        # dns_servers = ["************"]
        ports = ["port_http"]
        args  = [ ]
        volumes = [
           "local/dashy-config.yml:/app/user-data/conf.yml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

      }

      template {
        # NOTE we use the same configuration for prod and test - test is 
        #      basically a fallback option, and may be removed.
        data = file("../prod-obscol/assets/dashy-config.yml")
        destination = "local/dashy-config.yml"
      }

      resources {
        cpu = 400
        memory = 3000
      }

      service {
        name = "dash"
        port = "port_http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.dash.entrypoints=http,https",
          "traefik.http.routers.dash.rule=Host(`dash.obs.nsw.education`)",
          "traefik.http.routers.dash.tls=false",
        ]
        check {
          type = "http"
          port = "port_http"
          path = "/"
          interval = "30s"
          timeout = "5s"
        }
      }

    }
  }
}

// LDAP Password Rotator Nomad Job
// This job authenticates against an LDAP endpoint using credentials from Vault,
// rotates the password, and stores the new password back in Vault.

// Variables for configuration
variable "datacenter" {
  type = string
  default = "dc1"
  description = "Datacenter where the job will run"
}

variable "vault_policy" {
  type = string
  default = "ldap-password-rotation"
  description = "Vault policy that grants access to LDAP credentials"
}

variable "schedule" {
  type = string
  default = "0 0 1 * *"  // Monthly on the 1st at midnight
  description = "Cron schedule for password rotation"
}

variable "ldap_vault_path" {
  type = string
  default = "secret/ldap"
  description = "Path in Vault where LDAP credentials are stored"
}

job "ldap-password-rotator" {
  datacenters = [var.datacenter]
  type = "batch"

  periodic {
    cron = var.schedule
    prohibit_overlap = true
    time_zone = "UTC"
  }

  group "rotator" {
    count = 1

    restart {
      attempts = 3
      interval = "5m"
      delay = "15s"
      mode = "fail"
    }

    vault {
      policies = [var.vault_policy]
      change_mode = "restart"
    }

    task "rotate-password" {
      driver = "exec"

      config {
        command = "/bin/bash"
        args = ["local/rotate-ldap-password.sh"]
      }

      # We don't need this artifact section since we're creating the script with the template below
      # artifact {
      #   source = "local/rotate-ldap-password.sh"
      #   destination = "local/rotate-ldap-password.sh"
      #   mode = "file"
      # }

      # Environment variables for the script
      env {
        VAULT_ADDR = "http://active.vault.service.consul:8200"
        VAULT_LDAP_CONFIG_PATH = "${var.ldap_vault_path}/config"
        VAULT_LDAP_CREDS_PATH = "${var.ldap_vault_path}/service-account"
        LOG_LEVEL = "INFO"
      }

      # Template for the script with Vault integration
      template {
        data = <<EOF
#!/bin/bash
# LDAP Password Rotation Script
# This script retrieves LDAP credentials from Vault, rotates the password,
# and stores the new password back in Vault.

set -e

# Function to log messages with timestamp and level
log() {
  local level="$1"
  local message="$2"
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] [$level] $message"
}

# Function to log errors and exit
error_exit() {
  log "ERROR" "$1"
  exit 1
}

# Function to log info messages
info() {
  if [[ "$LOG_LEVEL" == "INFO" || "$LOG_LEVEL" == "DEBUG" ]]; then
    log "INFO" "$1"
  fi
}

# Function to log debug messages
debug() {
  if [[ "$LOG_LEVEL" == "DEBUG" ]]; then
    log "DEBUG" "$1"
  fi
}

# Check if vault command is available
if ! command -v vault &> /dev/null; then
  error_exit "Vault CLI is not installed or not in PATH"
fi

# Check if LDAP tools are installed
if ! command -v ldappasswd &> /dev/null; then
  info "Installing LDAP client tools..."
  apt-get update && apt-get install -y ldap-utils || error_exit "Failed to install LDAP tools"
fi

# Retrieve LDAP configuration from Vault
info "Retrieving LDAP configuration from Vault..."

# Get LDAP configuration
LDAP_HOST="{{ with secret "${VAULT_LDAP_CONFIG_PATH}" }}{{ .Data.data.host }}{{ end }}"
LDAP_PORT="{{ with secret "${VAULT_LDAP_CONFIG_PATH}" }}{{ .Data.data.port }}{{ end }}"
LDAP_BASE_DN="{{ with secret "${VAULT_LDAP_CONFIG_PATH}" }}{{ .Data.data.base_dn }}{{ end }}"

# Get current credentials
LDAP_SERVICE_ACCOUNT="{{ with secret "${VAULT_LDAP_CREDS_PATH}" }}{{ .Data.data.username }}{{ end }}"
LDAP_CURRENT_PASSWORD="{{ with secret "${VAULT_LDAP_CREDS_PATH}" }}{{ .Data.data.password }}{{ end }}"

# Validate required variables
[[ -z "$LDAP_HOST" ]] && error_exit "LDAP host not found in Vault"
[[ -z "$LDAP_PORT" ]] && error_exit "LDAP port not found in Vault"
[[ -z "$LDAP_BASE_DN" ]] && error_exit "LDAP base DN not found in Vault"
[[ -z "$LDAP_SERVICE_ACCOUNT" ]] && error_exit "LDAP service account not found in Vault"
[[ -z "$LDAP_CURRENT_PASSWORD" ]] && error_exit "LDAP current password not found in Vault"

info "LDAP configuration retrieved successfully"
info "Service account: $LDAP_SERVICE_ACCOUNT"

# Generate a new password
# Complex password with uppercase, lowercase, numbers, and special characters
NEW_PASSWORD=$(openssl rand -base64 32 | tr -dc 'a-zA-Z0-9!@#$%^&*()_+' | head -c 24)

info "Generated new password"

# Change the password
info "Changing LDAP password..."
LDAPTLS_REQCERT=never ldappasswd -H ldaps://$LDAP_HOST:$LDAP_PORT \
  -D "$LDAP_SERVICE_ACCOUNT" \
  -w "$LDAP_CURRENT_PASSWORD" \
  -s "$NEW_PASSWORD" \
  "$LDAP_SERVICE_ACCOUNT" || error_exit "Failed to change LDAP password"

# Verify the new password works
info "Verifying new password..."
if LDAPTLS_REQCERT=never ldapsearch -H ldaps://$LDAP_HOST:$LDAP_PORT \
  -D "$LDAP_SERVICE_ACCOUNT" \
  -w "$NEW_PASSWORD" \
  -b "$LDAP_BASE_DN" \
  -s base "objectclass=*" > /dev/null; then

  info "Password verification successful"

  # Store the new password in Vault
  info "Storing new password in Vault..."
  vault kv put ${VAULT_LDAP_CREDS_PATH} password="$NEW_PASSWORD" username="$LDAP_SERVICE_ACCOUNT" || \
    error_exit "Failed to store new password in Vault"

  info "Password rotation completed successfully"
else
  error_exit "Password verification failed! Manual intervention required."
fi

exit 0
EOF
        destination = "local/rotate-ldap-password.sh"
        perms = "0755"
      }

      resources {
        cpu    = 200
        memory = 256
      }

      # Add logging
      logs {
        max_files     = 10
        max_file_size = 15
      }
    }
  }

  # Provide a description of the job for operators
  meta {
    description = "Rotates LDAP service account password and updates Vault"
    version = "1.0.0"
    owner = "Security Team"
  }
}

#!/usr/bin/env python3
"""
<PERSON>ript to remove all "domain": "workgroup" entries from the JSON file.
This script will remove the entire line containing "domain": "workgroup" and the preceding comma if needed.
"""

import re
import sys

def remove_workgroup_domains(file_path):
    """Remove all 'domain': 'workgroup' entries from the JSON file."""
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Pattern to match lines with "domain": "workgroup" including the preceding comma and whitespace
    # This pattern handles both cases:
    # 1. "cir_app_id": "something",\n            "domain": "workgroup"
    # 2. "domain": "workgroup",\n            "next_field": "value"
    
    # First, remove lines that end with "domain": "workgroup" (no trailing comma)
    pattern1 = r',\s*\n\s*"domain":\s*"workgroup"'
    content = re.sub(pattern1, '', content)
    
    # Then, remove lines that have "domain": "workgroup", (with trailing comma)
    pattern2 = r'\n\s*"domain":\s*"workgroup",\s*'
    content = re.sub(pattern2, '\n', content)
    
    # Write the modified content back
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"Successfully removed all 'domain': 'workgroup' entries from {file_path}")

if __name__ == "__main__":
    file_path = "consul-services/prod/services.auto.tfvars.json"
    remove_workgroup_domains(file_path)

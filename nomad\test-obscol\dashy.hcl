# dashy - quick-access dashboard - test obscol

variables {
  env = "test"
}

job "dashy"  {
  datacenters = ["dc-cir-un-${var.env}"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

// Constraint added as a temporary fix to the coexistance issues across the test boxes
  // constraint {
  //   attribute = "${attr.unique.hostname}"
  //   operator = "regexp"
  //   value = "tl0992obscol0[5]"
  // }

  group "dashy" {
    network {
      port "port_http" {
        to = 8080
      }
    }

    #volume "vol_dashy"  {
    #  type = "host"
    #  source = "vol_dashy"
    #  read_only = false
    #}

    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    task "dashy" {
      driver = "docker"

      env {
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.education.nsw.gov.au,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        }

      #volume_mount {
      #  volume = "vol_dashy"
      #  destination = "/dashy"
      #  read_only = false
      #}

      config {
        image = "lissy93/dashy:latest"

        hostname = "dash"

        # dns_servers = ["************"]
        # 2023-03-29 - removed as part of consul / roundrobin resolution

        ports = ["port_http"]

        args  = [ ]
        volumes = [
#          "/dashy/conf.yml:/app/public/conf.yml",
           "local/dashy-config.yml:/app/user-data/conf.yml",
#          "local/dashy/public:/app/public",
#          "local/main.py:/main.py",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.test.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env}"
          }
        }

      }

      template {
        # NOTE we use the same configuration for prod and test - test is basically a fallback option, and may be removed
        # 2023-11-21 trying to resolve unique pathing to single config file
        # data = file("assets/dashy-config.yml")
        data = file("../../nomad/prod-obscol/assets/dashy-config.yml")

        destination = "local/dashy-config.yml"
      }

      resources {
        cpu = 400
        memory = 3000
      }

      service {
        name = "dash"
        port = "port_http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.dash.entrypoints=http,https",
          "traefik.http.routers.dash.rule=Host(`dash.obs.test.nsw.education`)",
          "traefik.http.routers.dash.tls=false"
        ]

        check {
          type = "http"
          port = "port_http"
          path = "/"
          interval = "30s"
          timeout = "5s"
        }

      }

    }
  }
}

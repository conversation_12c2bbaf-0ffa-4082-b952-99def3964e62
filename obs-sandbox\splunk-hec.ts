// https://github.com/sindresorhus/got
import got from 'got'
let splunkLog = got.post('https://http-inputs-nswdoe.splunkcloud.com/services/collector/event', {
  headers: {
    Authorization: 'Splunk 8c337d9a-efe6-4043-b65f-c54a31e2716c'
  },
  body: JSON.stringify({
    index: 'mtm',
    time: new Date().getTime(),
    host: 'swsonlinepayments.dev.education.nsw.gov.au',
    source: 'sws_online_payments_dev',
    sourcetype: 'Lambda:processFormSubmission:Info',
    event: {
      foo: 'Hello world',
      bar: 'Goodbye world'
    }
  })
})
{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 1, "iteration": 1604978352721, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": null, "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.1", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"bucketAggs": [{"field": "@timestamp", "id": "2", "settings": {"interval": "auto", "min_doc_count": 0, "trimEdges": 0}, "type": "date_histogram"}], "metrics": [{"field": "prometheus.metrics.nomad_client_host_memory_available", "id": "1", "meta": {}, "settings": {}, "type": "avg"}], "query": "prometheus.labels.host:$host", "refId": "A", "timeField": "@timestamp"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Nomad Available Host Memory ", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "decbytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 26, "style": "dark", "tags": [], "templating": {"list": [{"allValue": null, "current": {"selected": true, "text": "dl0992obsecol1.nsw.education", "value": "dl0992obsecol1.nsw.education"}, "datasource": "Elasticsearch", "definition": "{\"find\": \"terms\", \"field\": \"prometheus.labels.host\", \"size\": 1000}", "error": null, "hide": 0, "includeAll": false, "label": "Hostname", "multi": false, "name": "host", "options": [{"selected": true, "text": "dl0992obsecol1.nsw.education", "value": "dl0992obsecol1.nsw.education"}, {"selected": false, "text": "dl0992obsecol3.nsw.education", "value": "dl0992obsecol3.nsw.education"}], "query": "{\"find\": \"terms\", \"field\": \"prometheus.labels.host\", \"size\": 1000}", "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Nomad Node Status", "uid": "Up1rl_2Gk", "version": 1}
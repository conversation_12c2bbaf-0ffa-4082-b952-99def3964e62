
        #     - pa0991vivcr002.vi.det.nsw.edu.au #silverwater
        #     - pa0992vivcr001.vi.det.nsw.edu.au #unanderra
        #       VSPHERE_USER = "srv_obs-app-vcenter"
        #       VSPHERE_PASSWORD = "phuJ2jofrochlDr*fike"
# receivers:
#   vcenter:
#     endpoint: http://localhost:15672
#     username: otelu
#     password: ${env:VCENTER_PASSWORD}
#     collection_interval: 5m
#     initial_delay: 1s
#     metrics: []


receivers:
  prometheus: # This is the default configuration for Prometheus to scrape metrics from the collector
    config:
      scrape_configs:
      - job_name: vcenter-silverwater
        scrape_interval: 60s
        scheme: https
        metrics_path: /metrics
        static_configs:
        - targets: ['vcenter-silverwater.obs.nsw.education']

  vcenter:
    endpoint: https://pa0991vivcr002.vi.det.nsw.edu.au
    tls:
      insecure: true
    username: srv_obs-app-vcenter
    password: phuJ2jofrochlDr*fike
    collection_interval: 5m
    initial_delay: 10s
    # metrics:
    #     vcenter.host.cpu.utilization:
    #       enabled: true

processors:
  resource:
    attributes: # This processor adds attributes to logs, we use it to add the service name and provenance to logs
        # Add service_name 
        - key: service.name # a gotcha here is that you cant use underscores in the key, it will be converted to dots, just use the dots here.
          action: upsert
          value: "vcenter-silverwater"

  batch:
    timeout: 1s
    send_batch_size: 1024
    send_batch_max_size: 4096   
  memory_limiter: # This is the default memory limiter configuration, it should be less than the memory limit of the Nomad job
    limit_mib: 4098
    spike_limit_mib: 3500 # This is the maximum memory that the collector can use, it can be the same as the nomad job memory limit
    check_interval: 5s 

extensions:
  health_check:
    endpoint: "0.0.0.0:{{ env "NOMAD_PORT_healthcheck" }}"
    path: "/health/status"

#basic auth to grafanacloud OTLP gateway
  basicauth/otlp:
    client_auth:
      username: '533612'
      password: ****************************************************************************************************************************************************

# Exporters are defined here
exporters:
  debug: # Debug is a otlp native exporter that can be used to debug the collector
    verbosity: detailed

# GrafanaMimir onpremise endpoint
  prometheusremotewrite/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
    headers:
      X-Scope-ORGID: prod

# Loki OSS onpremise endpoint
  otlphttp/onpremloki:
    endpoint: "https://loki.obs.nsw.education/otlp"   

# GrafanaCloud has a simple gateway for OTLP
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

service:
  extensions: [health_check,basicauth/otlp]

  pipelines:
    metrics:
      receivers: [vcenter,prometheus]
      processors: []
      exporters: [prometheusremotewrite/onpremmimir] # if you want to debug this collector in the stout you can add the 'debug' exporter here

  telemetry:
    logs:
      processors:
        - batch:
            exporter:
              otlp:
                protocol: http/protobuf
                endpoint: https://loki.obs.nsw.education/otlp/v1/logs
    metrics:
      readers:
        - pull:
            exporter:
              prometheus:
                host: '0.0.0.0'
                port: {{ env "NOMAD_PORT_metrics" }}
      level: detailed
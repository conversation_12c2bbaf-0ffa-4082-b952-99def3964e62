
# garage - cluster mode - 3 nodes (should really be 4, but meh) for DG, running on dg-hac-0[123]
#
# PoC - obviously NOT actual ha, as it all runs on the same vmware host, sharing the same platters.

# garage == distributed object storage (s3 or minio -alike)

# Refer:
#   https://garagehq.deuxfleurs.fr/documentation/quick-start/
#   and
#   accompanying `garage-README.md` that describes initial configuration of cluster.

// 2025-03-07 - abstracting to pick up target datacenter based on shell
//              environment variable `NOMAD_DC` - which MUST be set, with
//              a value of either 'DG' or 'PY'.


// Refer traefik.nomad for discussion about nomad_dc and locals{}
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}

locals {
  image_garage = "dxflrs/garage:v1.0.0"
  # image_garage = var.nomad_dc == "DG" ? "dg-pan-01.int.jeddi.org:5000/garage:v1.0.0" : "registry.obs.int.jeddi.org/garage:v1.0.0"
  host_constraint = var.nomad_dc == "DG" ? "dg-hac-0[123]" : "py-hac-0[123]"
  # loki_url = "https://loki.obs.int.jeddi.org/loki/api/v1/push"
  loki_url = "https://loki-rwb.obs.int.jeddi.org/loki/api/v1/push"
}


variables {
  # IFF garage is running, we can use it - this works with the HA, but
  # if garage is administratively DOWN we need to revert to docker.io
  # image_garage = "dxflrs/garage:v0.9.1"
  # image_garage = "dg-pan-01.int.jeddi.org:5000/garage:v1.0.0"
}

# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "garage" {

  datacenters = [ var.nomad_dc ]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = local.host_constraint
   }

# Group garage  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "garage" {

    count = 3
    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    network {
      # We don't use Traefik - instead we use static ports, and static task-per-host.
      port "port_3900" {
        static = 3900
      }
      port "port_3901" {
        static = 3901
      }
      port "port_3902" {
        static = 3902
      }
      port "port_3903" {
        static = 3903
      }
      port "port_3904" {
        static = 3904
      }
    }

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    task "garage-task" {
      driver = "docker"

      env = {
        # The only way to tune log verbosity evidently is via the RUST_LOG env var
        # Available log levels are:  error, warn, info (default), debug, trace.
        # RUST_LOG=garage=info garage server # default
        "RUST_LOG" = "garage=warn"
      }

      config {
        image = local.image_garage

        # ports = ["port_api", "port_console"]
        ports = ["port_3900", "port_3901", "port_3902", "port_3903", "port_3904"]

        args = [ 
#          "-c",   "/etc/garage.toml",
#          "server", 
#          "--address",           "0.0.0.0:${NOMAD_HOST_PORT_port_api}",
#          "--console-address",   "0.0.0.0:${NOMAD_HOST_PORT_port_console}",
#          # "/data",
#          "http://dg-hac-0{1...3}.obs.int.jeddi.org:${NOMAD_PORT_port_api}/data",

        ]

        volumes = [
          "local/garage-cluster.toml:/etc/garage.toml",
          "/opt/sharednfs/garage:/persistent/"
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }        

      }

      resources {
        cpu = 120
        memory = 200
        memory_max = 400
      }


      # Multi-host / cluster configuration file - runs on dg-hac-01, 02, 03
      # with each host using /opt/sharednfs/garage/dg-hac-{hostid}/ for storage.
      template {
        data = <<EOH
# metadata_dir = "/var/lib/garage/meta"
# data_dir = "/var/lib/garage/data"

metadata_dir = "/persistent/{{ env "attr.unique.hostname" }}/meta"
data_dir = "/persistent/{{ env "attr.unique.hostname" }}/data"

db_engine = "lmdb"

# 2025-03 configuration syntax change - replication _ mode is deprecated in
#         favour of both replication_factor and consistency_mode.  The latter
#         can be 'consistent', 'degraded' and 'dangerous'. The latter two drop
#         the read-quorum, or both read and write quorums, down to 1.
# replication _ mode = "3"
replication_factor = 3
consistency_mode = "consistent"

compression_level = 2

rpc_bind_addr = "[::]:3901"
#rpc_public_addr = "<this node's public IP>:3901"
rpc_public_addr = "{{ env "NOMAD_IP_port_3901" }}:3901"
rpc_secret = "76a1e060ac0a36f7e0c7e4215cd8f4eb765659e2ffe0dbfb8075b535550456c5"

[s3_api]
s3_region = "garage"
api_bind_addr = "[::]:3900"
root_domain = ".s3.garage"

[s3_web]
bind_addr = "[::]:3902"
root_domain = ".web.garage"
index = "index.html"

[admin]
# This gives us /metrics (prometheus scrapeable)
api_bind_addr = "0.0.0.0:3903"

EOH
        destination = "local/garage-cluster.toml"
        perms = "755"
      }



      # = = = = = = = = = = = = = = = = = = = = = = = = = = = =
      # garage - single node - we're not using this, but retaining the configuration
      # here for reference.  Refer the multi-node / cluster configuration file above.
      template {
        data = <<EOH
metadata_dir = "/tmp/meta"
data_dir = "/tmp/data"
db_engine = "lmdb"

replication_mode = "none"

rpc_bind_addr = "[::]:3901"
rpc_public_addr = "127.0.0.1:3901"
# rpc_secret = "$(openssl rand -hex 32)"
rpc_secret = "76a1e060ac0a36f7e0c7e4215cd8f4eb765659e2ffe0dbfb8075b535550456c5"

[s3_api]
s3_region = "garage"
api_bind_addr = "[::]:3900"
root_domain = ".s3.garage.localhost"

[s3_web]
bind_addr = "[::]:3902"
root_domain = ".web.garage.localhost"
index = "index.html"

[k2v_api]
api_bind_addr = "[::]:3904"

[admin]
api_bind_addr = "0.0.0.0:3903"
admin_token = "$(openssl rand -base64 32)"


EOH
        destination = "local/garage-single.toml"
        perms = "755"
      }
      # = = = = = = = = = = = = = = = = = = = = = = = = = = = =

      service {
        name = "garage-s3"
        port = "port_3900"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.garage-s3.rule=Host(`garage-s3.obs.int.jeddi.org`)",
          "traefik.http.routers.garage-s3.entrypoints=http,https",
          # "traefik.http.routers.garage-s3.tls=false",
        ]
      }

      service {
        name = "garage"
        port = "port_3902"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.garage.rule=Host(`garage.obs.int.jeddi.org`)",
          "traefik.http.routers.garage.entrypoints=http,https",
          # "traefik.http.routers.garage.tls=false",
        ]
      }

    }  // end-task garage

  } // end-group garage

} // end-job garage

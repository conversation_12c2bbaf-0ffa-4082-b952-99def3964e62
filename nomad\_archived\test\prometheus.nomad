job "prometheus" {
  type = "service"
  datacenters = ["dc-un-test"]

  group "prometheus" {

    network {
      port "prometheus" {
        static = 9090
      }
    }

    task "prometheus" {
      driver = "docker"

      config {
        ports = ["prometheus"]
        image = "https://docker.io/prom/prometheus:v2.29.1"
        dns_servers = ["192.168.31.1"]

        port_map {
          prometheus = 9090
        }

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "local/prometheus-configuration/prometheus/rules:/etc/prometheus/rules.d"
        ]
      }

      service {
        name = "prometheus-http"
        port = "prometheus"

        check {
          type = "http"
          port = "prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      artifact {
        source = "git::ssh://****************************:7999/obs/prometheus-configuration.git"
        destination = "local/prometheus-configuration"

        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'alertmanager'
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['alertmanager']

  - job_name: 'nomad'
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['nomad-client', 'nomad']
        tags: ['http']

  # Any external node that runs telegraf
  - job_name: 'telegraf'
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['telegraf']

  # Any external node that runs telegraf
  - job_name: 'openmetrics'
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['openmetrics']
    relabel_configs:
      - source_labels: [__meta_consul_metadata_cir_app_id]
        target_label: cir_app_id
      - source_labels: [__meta_consul_metadata_env]
        target_label: env

  # Any standalone exporter that lives in the nomad cluster and not the agent
  - job_name: 'prometheus-exporter'
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['prometheus-exporter']

  # Any standalone rubrik exporter that lives in the nomad cluster and not the agent
  - job_name: 'rubrik-exporter'
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['rubrik-exporter']

  - job_name: 'nifi'
    metrics_path: /metrics/
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['nifi']

  - job_name: 'ssl'
    metrics_path: /probe
    params:
      module: ["https"]
    consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-cir-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['https']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: ssl-exporter.service.dc-cir-un-test.collectors.obs.test.nsw.education:9219

#    relabel_configs:
#      - source_labels: ['__meta_consul_tags']
#        regex: '(.*)http(.*)'
#        action: keep

rule_files:
  - /etc/prometheus/rules.d/*.rules
  - /etc/prometheus/rules.d/*.yaml
  - /etc/prometheus/rules.d/*.yml

alerting:
  alertmanagers:
    - consul_sd_configs:
      - server: 'consul.service.dc-un-test.collectors.obs.test.nsw.education:8500'
        datacenter: 'dc-un-test'
        token: 'db792090-90e2-ca66-7036-31fe3d312e17'
        services: ['alertmanager']

remote_write:
  - name: cortex
    url: "http://cortex.apps.s0.ocp.svcs.education.nsw.gov.au/api/v1/push"
EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 600
        memory = 2048
      }

    }
  }
}

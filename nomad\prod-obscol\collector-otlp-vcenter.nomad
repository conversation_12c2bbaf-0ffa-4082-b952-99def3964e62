# OTLP collector receiver/exporter for Splunk HEC endpoint for the SAP PowerConnect integration

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml



variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol" #0.122.0
}

job "collector-otlp-vcenter" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }
  
  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = "pl0992obscol0[123]"
  }  

  group "vcenter-silverwater" {
    count = 1

    network {
      port "metrics" {
        to = 8888
      }
      port "healthcheck" {
        to = 13133
      }
      }

    task "vcenter-silverwater" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false
        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        ports = [
            "metrics",
            "healthcheck"
        ]
      }
      resources {
        cpu    = 2000
        memory = 2000
      }
      template {
        data = file("assets/collector-otlp-vcenter-silverwater.yaml")
        destination   = "local/otel/config.yaml"
      }
    service {
      name     = "collector-otlp-vcenter-metrics-silverwater"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.collector-otlp-vcenter-silverwater-metrics.entrypoints=http,https",
        "traefik.http.routers.collector-otlp-vcenter-silverwater-metrics-metrics.rule=Host(`vcenter-silverwater.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.collector-otlp-vcenter-silverwater-metrics-metrics.tls=false"
      ]
    }
    service {
      name     = "collector-otlp-vcenter-silverwater-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.collector-otlp-vcenter-silverwater-healthcheck.entrypoints=https",
        "traefik.http.routers.collector-otlp-vcenter-silverwater-healthcheck.rule=Host(`vcenter-silverwater.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.collector-otlp-vcenter-silverwater-healthcheck.tls=false"
      ]
    }  
    }
  }
  group "vcenter-unanderra" {
    count = 1

    network {
      port "metrics" {
        to = 8888
      }
      port "healthcheck" {
        to = 13133
      }
      }

    task "vcenter-unanderra" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false
        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        ports = [
            "metrics",
            "healthcheck"
        ]
      }
      resources {
        cpu    = 2000
        memory = 2000
      }
      template {
        data = file("assets/collector-otlp-vcenter-unanderra.yaml")
        destination   = "local/otel/config.yaml"
      }
    service {
      name     = "collector-otlp-vcenter-metrics-unanderra"
      port     = "metrics"
      provider = "consul"
     check {
        type = "http"
        path = "/metrics"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.collector-otlp-vcenter-unanderra-metrics.entrypoints=http,https",
        "traefik.http.routers.collector-otlp-vcenter-unanderra-metrics-metrics.rule=Host(`vcenter-unanderra.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.collector-otlp-vcenter-unanderra-metrics-metrics.tls=false"
      ]
    }
    service {
      name     = "collector-otlp-vcenter-unanderra-healthcheck"
      port     = "healthcheck"
      provider = "consul"
      check {
        type = "http"
        path = "/health/status"
        interval = "10s"
        timeout = "5s"
      }
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.collector-otlp-vcenter-unanderra-healthcheck.entrypoints=https",
        "traefik.http.routers.collector-otlp-vcenter-unanderra-healthcheck.rule=Host(`vcenter-unanderra.obs.nsw.education`) && Path(`/health/status`)",
        "traefik.http.routers.collector-otlp-vcenter-unanderra-healthcheck.tls=false"
      ]
    }  
    }
  }  
}
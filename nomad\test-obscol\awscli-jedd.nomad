
# generic awscli tool

job "awscli" {

  datacenters = ["dc-cir-un-prod"]

  type = "service"

  namespace = "default"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "control" {

    network {
      port "port_awscli" { }
      mode = "host"
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      # value = "tl0992obscol01"
      value = "pl0475obscol06"
    }

    task "awscli" {
      driver = "docker"

      env = {
        # We do NOT want these enabled, but occasionally for testing
        #HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        #HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
      }


      config {
        image = "amazon/aws-cli"

        ports = ["port_awscli"]

        entrypoint = [ "/usr/bin/sleep" ]

        args = [" infinity"]

        volumes = [
#          "/local/bashrc:/root/.bashrc"
          "local/:/root/"
        ]
      }

      resources {
        cpu = 500
        memory = 500
      }
      template {
        data        = <<EOH
export PS1="\[\e[33;1m\]AWS CLI docker:\w# \[\e[0m\]"
export TERM=linux
EOH
        destination = "local/.bashrc"
        # destination = "/root/.bashrc"
      }

      service {
        name = "awscli"
        port = "port_awscli"

#        check {
#          name     = "alive"
#          type     = "tcp"
#          interval = "10s"
#          timeout  = "2s"
#        }

      }

    }

  }
}

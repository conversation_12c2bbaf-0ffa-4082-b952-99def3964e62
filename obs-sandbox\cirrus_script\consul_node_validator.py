import requests
import logging
from requests.exceptions import RequestException
import sys
import socket

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)


# Consul configuration
#CONSUL_HOST = 'tl0992obscol01.nsw.education'  # Replace with your Consul host
CONSUL_HOST = 'pl0992obscol01.nsw.education'  # Replace with your Consul host
CONSUL_PORT = 8500         # Replace with your Consul port if different
#CONSUL_TOKEN = 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'  #TEST Replace with your Consul token
CONSUL_TOKEN = 'b68e1c4b-dac2-990b-1743-0d13056b56a5'  #PROD Replace with your Consul token
CONSUL_BASE_URL = f'http://{CONSUL_HOST}:{CONSUL_PORT}'
CONSUL_API_VERSION = 'v1'

# Port check configuration
CHECK_PORT = 11055
CHECK_TIMEOUT = 1  # seconds
#OUTPUT_FILE = "test_unreachable_nodes.txt"  # File to store unreachable nodes
OUTPUT_FILE = "prod_unreachable_nodes.txt"  # File to store unreachable nodes

# Set headers with the Consul token
HEADERS = {
    'X-Consul-Token': CONSUL_TOKEN
}

def get_consul_services():
    """
    Fetches the list of services from Consul.
    """
    url = f"{CONSUL_BASE_URL}/v1/catalog/services"
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        services = response.json()
        logging.info(f"Fetched {len(services)} services from Consul.")
        return services
    except requests.RequestException as e:
        logging.error(f"Error fetching services from Consul: {e}")
        return {}

def get_nodes_for_service(service_name):
    """
    Fetches the list of nodes running the given service from Consul.
    """
    url = f"{CONSUL_BASE_URL}/v1/catalog/service/{service_name}"
    try:
        response = requests.get(url, headers=HEADERS, timeout=10)
        response.raise_for_status()
        nodes = response.json()
        logging.info(f"Fetched {len(nodes)} nodes for service {service_name}.")
        return nodes
    except requests.RequestException as e:
        logging.error(f"Error fetching nodes for service {service_name}: {e}")
        return []

def check_node_health(node_id, node_address):
    """
    Checks if the node is reachable via TCP on the specified port.
    """
    try:
        with socket.create_connection((node_address, CHECK_PORT), timeout=CHECK_TIMEOUT) as sock:
            logging.info(f"Node {node_id} ({node_address}:{CHECK_PORT}) is reachable.")
            return True
    except (socket.timeout, ConnectionRefusedError) as e:
        logging.warning(f"Failed to reach node {node_id} ({node_address}:{CHECK_PORT}): {e}")
        return False
    except Exception as e:
        logging.error(f"Unexpected error reaching node {node_id} ({node_address}:{CHECK_PORT}): {e}")
        return False

def deregister_node(node_id):
    """
    Deregisters the node from Consul using its node ID.
    """
    url = f"{CONSUL_BASE_URL}/v1/catalog/deregister"
    payload = {"Node": node_id}
    try:
        response = requests.put(url, headers=HEADERS, json=payload, timeout=10)
        response.raise_for_status()
        logging.info(f"Successfully deregistered node {node_id} from Consul.")
    except requests.RequestException as e:
        logging.error(f"Error deregistering node {node_id}: {e}")

def output_unreachable_nodes_to_file(unreachable_nodes):
    """
    Writes the list of unreachable nodes to a file.
    """
    with open(OUTPUT_FILE, 'w') as file:
        for node in unreachable_nodes:
            file.write(f"{node['Node']} ({node['Address']})\n")
    logging.info(f"Unreachable nodes saved to {OUTPUT_FILE}")

def prompt_for_bulk_deregister(unreachable_nodes):
    """
    Prompts the user to confirm before bulk deregistering unreachable nodes.
    """
    while True:
        user_input = input(f"{len(unreachable_nodes)} nodes are unreachable. "
                           f"Do you want to deregister all of them from Consul? (y/n): ").strip().lower()
        if user_input == 'y':
            return True  # Proceed with bulk deregistration
        elif user_input == 'n':
            logging.info("Skipping bulk deregistration.")
            return False  # Skip bulk deregistration
        else:
            logging.warning("Invalid input. Please enter 'y' to deregister or 'n' to skip.")

def main():
    # Step 1: Fetch all services from Consul
    services = get_consul_services()
    
    if not services:
        logging.info("No services to process. Exiting.")
        return

    # Step 2: Filter services starting with 'openmetrics_'
    openmetrics_services = {name: tags for name, tags in services.items() if name.startswith('openmetrics_')}
    if not openmetrics_services:
        logging.info("No 'openmetrics_' services found. Exiting.")
        return

    unreachable_nodes = []
    
    # Step 3: For each openmetrics service, fetch associated nodes and check their health
    for service_name in openmetrics_services:
        nodes = get_nodes_for_service(service_name)
        for node in nodes:
            node_id = node.get('Node')
            node_address = node.get('Address')
            if not node_address:
                logging.warning(f"Node {node_id} does not have an address. Skipping.")
                continue

            is_reachable = check_node_health(node_id, node_address)
            if not is_reachable:
                logging.info(f"Node {node_id} ({node_address}) is unreachable.")
                unreachable_nodes.append(node)
    
    # Step 4: Output unreachable nodes to file
    if unreachable_nodes:
        output_unreachable_nodes_to_file(unreachable_nodes)
        
        # Ask for confirmation to deregister nodes in bulk
        if prompt_for_bulk_deregister(unreachable_nodes):
            for node in unreachable_nodes:
                deregister_node(node['Node'])
        else:
            logging.info("No nodes were deregistered.")
    else:
        logging.info("All nodes are reachable. No nodes to deregister.")

if __name__ == "__main__":
    main()
job "test-ipmi-simulator" {
  datacenters = ["dev"]
  type = "service"

  group "collector" {
    count = 1

    task "ipmi-sim" {
      driver = "docker"

      config {
        # https://github.com/rhtyd/ipmisim-docker
        image = "bhaisaab/ipmi_sim:latest"

        port_map {
          ipmi = 9001
        }
      }

      resources {
        network {
          port "ipmi" {}
        }
      }

      service {
        name = "ipmi-integration-test"
        port = "ipmi"
      }
    }
  }
}
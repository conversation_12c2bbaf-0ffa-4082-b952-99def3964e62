job "generic-snmptraps" {
  # SNMPTRAPS should probably be completely different to the 3.4 implementation.
  # We should try to use the upstream container zabbix/zabbix-snmptraps


  datacenters = ["dc-un-test"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "tu0992tcdnd001.hbm.det.nsw.edu.au"
    }


    task "zabbix-proxy" {
      driver = "docker"

      config {
        image = "https://artifacts.mtm.nsw.education/zabbix-proxy-doe:centos-5.0.2"
        hostname = "collector-generic-snmptraps.mtm.test.det.nsw.edu.au"
        # extra_hosts = ["dl0992obszbs01.nsw.education:*************"]
        dns_servers = ["************"]  # dnsmasq on container host

        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }

        volumes = [
          "${NOMAD_ALLOC_DIR}/snmptraps:/var/lib/zabbix/snmptraps"
        ]
      }

      service {
        name = "generic-snmptraps-passive"
        port = "zabbix_passive"

        check {
          type = "tcp"
          port = "zabbix_passive"
          interval = "20s"
          timeout = "10s"
        }
      }

      service {
        name = "generic-snmptraps"
        port = "zabbix_server"

        check {
          type = "tcp"
          port = "zabbix_server"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        ZBX_HOSTNAME = "collector-generic-snmptraps.mtm.test.det.nsw.edu.au"
        ZBX_SERVER_HOST = "tu0992tedbd001.hbm.det.nsw.edu.au"
        DB_SERVER_HOST = "${NOMAD_IP_db_postgresql}"
        DB_SERVER_PORT = "${NOMAD_PORT_db_postgresql}"
        ZBX_PROXYOFFLINEBUFFER = "3"
        ZBX_STARTPOLLERS = "32"
        ZBX_STARTPOLLERSUNREACHABLE = "16"
        ZBX_STARTTRAPPERS = "15"
        ZBX_STARTPINGERS = "16"
        ZBX_STARTDISCOVERERS = "8"
        ZBX_CACHESIZE = "256M"
        ZBX_HISTORYCACHESIZE = "128M"
        ZBX_HISTORYINDEXCACHESIZE = "32M"
        ZBX_UNREACHABLEPERIOD = "120"
        ZBX_UNREACHABLEDELAY = "30"


        # "ZBX_PROXYMODE" = "0"
        # "ZBX_SERVER_PORT" = "10051"
        # "ZBX_LOADMODULE" = ""
        # "ZBX_DEBUGLEVEL" = "3"
        # "ZBX_JAVAGATEWAY_ENABLE" = "false"

        # "ZBX_PROXYLOCALBUFFER" = "0"
        # "ZBX_PROXYHEARTBEATFREQUENCY" = "60"
        # "ZBX_DATASENDERFREQUENCY" = "1"
        # "ZBX_IPMIPOLLERS" = "0"
        # "ZBX_STARTHTTPPOLLERS" = "1"

        # "ZBX_JAVAGATEWAY" = "zabbix-java-gateway"
        # "ZBX_JAVAGATEWAYPORT" = "10052"
        # "ZBX_STARTJAVAPOLLERS" = "0"

        # "ZBX_STARTVMWARECOLLECTORS" = "0"
        # "ZBX_VMWAREFREQUENCY" = "60"
        # "ZBX_VMWARECACHESIZE" = "8M"
        ZBX_ENABLE_SNMP_TRAPS = "true"  # No need to specify trap directory as we use the defaults
        # "ZBX_HOUSEKEEPINGFREQUENCY" = "1"
        # "ZBX_STARTDBSYNCERS" = "4"
        # "ZBX_TRAPPERIMEOUT" = "300"
        # "ZBX_UNAVAILABLEDELAY" = "60"

        # "ZBX_TLSCONNECT" = "unencrypted"
        # "ZBX_TLSACCEPT" = "unencrypted"
        # "ZBX_TLSCAFILE" = ""
        # "ZBX_TLSCRLFILE" = ""
        # "ZBX_TLSSERVERCERTISSUER" = ""
        # "ZBX_TLSSERVERCERTSUBJECT" = ""
        # "ZBX_TLSCERTFILE" = ""
        # "ZBX_TLSKEYFILE" = ""
        # "ZBX_TLSPSKIDENTITY" = ""
        # "ZBX_TLSPSKFILE" = ""

        # "ZBX_ENABLEREMOTECOMMANDS" = "0" # Available since 3.4.0
        # "ZBX_LOGREMOTECOMMANDS" = "0" # Available since 3.4.0
        # "ZBX_HOSTNAMEITEM" = "system.hostname"
        # "ZBX_SOURCEIP" = ""
        # "ZBX_VMWAREPERFFREQUENCY" = "60"
        # "ZBX_VMWARETIMEOUT" = "10"
        # "ZBX_LISTENIP" = ""
      }

      resources {
        network {
          port "zabbix_passive" {}
          port "zabbix_server" {
            static = 10065
          }
        }
      }
    }

    task "zabbix-snmptraps" {
      driver = "docker"

      config {
        image = "artifacts.mtm.nsw.education/zabbix-snmptraps:centos-4.2.8"

        port_map {
          snmp = 161
          snmptrap = 162
        }

        volumes = [
          "alloc/mibs:/var/lib/zabbix/mibs",
          "${NOMAD_ALLOC_DIR}/snmptraps:/var/lib/zabbix/snmptraps"
        ]
      }

      service {
        name = "collector-generic-snmptraps-trap"
        port = "snmptrap"
      }

      env {

      }

      resources {
        network {
          port "snmptrap" {
            static = 162
          }
        }
      }
    }

    task "db" {
      driver = "docker"

      config {
        image = "https://artifacts.mtm.nsw.education/postgres:12"

        port_map {
          postgresql = 5432
        }
      }

      service {
        name = "collector-generic-snmptraps-db"
        port = "postgresql"

        check {
          type = "tcp"
          port = "postgresql"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

      resources {
        network {
          port "postgresql" {}
        }
      }
    }
  }
}

global:
  scrape_interval:     15s # Set the scrape interval to every 15 seconds. Default is every 1 minute.
  evaluation_interval: 15s # Evaluate rules every 15 seconds. The default is every 1 minute.

# Alertmanager configuration
alerting:
  alertmanagers:
    - consul_sd_configs:
        - server: 'consul.service.dc1.consul:8500'
          datacenter: 'dc1'
          token: 'f25a7cea-703e-2790-52b2-907fcc1d5ca1'
          services:
            - 'alertmanager-http'

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
# - "first_rules.yml"
# - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'cluster'
    consul_sd_configs:
      - server: 'consul.service.dc1.consul:8500'
        datacenter: 'dc1'
        token: 'f25a7cea-703e-2790-52b2-907fcc1d5ca1'

remote_write:
  - url: "http://metricbeat-remote-write.service.dc1.consul:9201/write"

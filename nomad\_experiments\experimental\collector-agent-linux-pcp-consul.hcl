job "collector-agent-linux-pcp" {
  datacenters = ["dev"]
  type = "service"

  group "collector" {
    count = 1

    task "zabbix-proxy" {
      driver = "docker"

      config {
        image = "dl0992tbld0001.nsw.education/zabbix-proxy-postgresql:centos-4.0.6"
        hostname = "collector-agent-linux-pcp.mtm.dev.det.nsw.edu.au"
        command = "su zabbix -s /bin/bash -c '/usr/sbin/zabbix_proxy --foreground -c /etc/zabbix/zabbix_proxy.conf'"

        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }

        volumes = [
          "local/zabbix_proxy.conf:/etc/zabbix/zabbix_proxy.conf"
        ]
      }

      service {
        name = "collector-agent-linux-pcp"
        port = "zabbix_passive"
        tags = [
          "collector",
          "collector-linux"]
      }

      template {
        source = "/opt/nomad/templates/zabbix_proxy.conf.ctmpl"
        destination = "local/zabbix_proxy.conf"
        change_mode = "restart"
      }

      env {
      }

      resources {
        network {
          port "zabbix_passive" {}
          port "zabbix_server" {
            static = 10052
          }
        }
      }
    }

    task "db" {
      driver = "docker"

      config {
        image = "dl0992tbld0001.nsw.education/postgres:9.6"
        hostname = "collector-agent-linux-pcp-db.mtm.dev.det.nsw.edu.au"

        port_map {
          postgresql = 5432
        }

        volumes = [
          "/opt/nomad/database/postgresql_4.0.6/schema.sql:/docker-entrypoint-initdb.d/schema.sql"
        ]
      }

      service {
        name = "collector-agent-linux-pcp-db"
        port = "postgresql"
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

      resources {
        network {
          port "postgresql" {}
        }
      }
    }
  }
}
admin:
  access_log_path: /tmp/admin_access.log
  address:
    socket_address:
      protocol: TCP
      address: 0.0.0.0
      port_value: 9901
static_resources:
  listeners:
    - name: zabbix_proxy_listener
      address:
        socket_address:
          protocol: TCP
          address: 0.0.0.0
          port_value: 20051
      filter_chains:
        - filters:
            - name: envoy.tcp_proxy
              typed_config:
                "@type": type.googleapis.com/envoy.config.filter.network.tcp_proxy.v2.TcpProxy
                stat_prefix: tcp
                cluster: zabbix_proxy
                access_log:
                  - name: envoy.file_access_log
                    config:
                      path: /tmp/envoy.zabbix_proxy.log

  clusters:
    - name: zabbix_proxy
      type: STATIC
      connect_timeout: 0.25s
      lb_policy: ROUND_ROBIN
      load_assignment:
        cluster_name: zabbix_proxy
        endpoints:
          - lb_endpoints:
              - endpoint:
                  address:
                    socket_address:
                      address: ************
                      port_value: 10051
                      protocol: TCP

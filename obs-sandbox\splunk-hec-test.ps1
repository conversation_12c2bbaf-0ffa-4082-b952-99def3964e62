# Define the headers
$headers = @{
    'Authorization' = 'Splunk 8c337d9a-efe6-4043-b65f-c54a31e2716c'
    'Content-Type' = 'application/json'
}

# Get current timestamp in milliseconds
$timestamp = [DateTimeOffset]::Now.ToUnixTimeMilliseconds()

# Define the body as a PowerShell object that will be automatically converted to JSON
$body = @{
    index = 'mtm'
    time = $timestamp
    host = 'notify.dev.education.nsw.gov.au'
    source = 'school_notify_dev'
    sourcetype = 'Postman'
    event = @{
        foo = 'Hello world'
        bar = 'Goodbye world'
    }
}

# Define the URI
$uri = 'https://http-inputs-nswdoe.splunkcloud.com/services/collector'

# Make the request
$response = Invoke-RestMethod -Uri $uri `
                            -Method Post `
                            -Headers $headers `
                            -Body (ConvertTo-Json $body -Depth 10) `
                            -Verbose

# Display the response
$response
// test obscol - alertmanager - talking to prometheus

job "alertmanager" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "alertmanager" {

    network {
      port "http" {
        static = 9093
      }
    }

    task "alertmanager" {
      driver = "docker"

      config {
        ports = ["http"]
        image = "https://docker.io/prom/alertmanager:v0.22.2"
        dns_servers = ["192.168.31.1"]
        volumes = [
          "local/alertmanager.yaml:/etc/alertmanager/alertmanager.yml",
        ]
      }

      service {
        name = "alertmanager"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH

global:
  # mutethischannel hook
  slack_api_url: *****************************************************************************
  http_config:
    proxy_url: http://proxy.det.nsw.edu.au:80/

templates:
- '/etc/alertmanager/template/*.tmpl'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 3h
  receiver: default

receivers:
- name: default
  slack_configs:
    - channel: "#mutethischannel"
      send_resolved: true

EOH
        destination = "local/alertmanager.yaml"
      }
    }
  }
}

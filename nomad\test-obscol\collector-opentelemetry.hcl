# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:test-obscol"
}

job "collector-opentelemetry" {
  datacenters = ["dc-cir-un-test"]
  type        = "service"
  namespace = "collectors"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "otel-collector" {

    count = 1
  
  update {
    max_parallel = 1 #allows for there to be at least two instance running avoiding data dropouts
    health_check = "checks" #specifies the allocation should be considered healthy
    auto_revert = true #if job does not go healthy nomad will revert to previous version
    healthy_deadline = "5m" #Time allowed for job to report as healthy
    min_healthy_time = "5s" #Time to wait before checking job health
    canary = 1 #Spawns a new instance of the job and checks health before updating all instances
    auto_promote = true #Promotes the canary to the full job if healthy
  }

    network {

      port "healthcheck" {
        to = 13133
      }
      port "jaeger-grpc" {
        to = 14250
      }
      port "jaeger-thrift-http" {
        to = 14268
      }
      port "metrics" {
        to = 8888
      }
      port "influxdb"{
        to = 8086
      }
      port "loki-receiver" {
        to = 3500
      }
      port "otlp" {
        to = 4317
      }
      port "otlphttp" {
        to = 4318
      }
      port "zipkin" {
        to = 9411
      }
      port "zpages" {
        to = 55679
      }
      port "pprof" {
        to = 1777
      }
    }

    service {
      name     = "metrics"
      port     = "metrics"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel.entrypoints=http,https",
        "traefik.http.routers.otel.rule=Host(`otel.obs.test.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otel.tls=false"
      ]
    }

    service {
      name     = "loki-receiver"
      port     = "loki-receiver"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-loki.entrypoints=http,https",
        "traefik.http.routers.otel-loki.rule=Host(`otel.obs.test.nsw.education`) && Path(`/loki/api/v1/push`)",
        "traefik.http.routers.otel-loki.tls=false"
      ]
    }

    service {
      name     = "influxdb-receiver"
      port     = "influxdb"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-influxdb.entrypoints=http,https",
        "traefik.http.routers.otel-influxdb.rule=Host(`otel-influxdb.obs.test.nsw.education`) && Path(`/write`)",
        "traefik.http.routers.otel-influxdb.tls=false"
      ]
    }

    service {
      name     = "healthcheck"
      port     = "healthcheck"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-healthcheck.entrypoints=https",
        "traefik.http.routers.otel-healthcheck.rule=Host(`otel.obs.test.nsw.education`) && Path(`/healthcheck`)",
        "traefik.http.routers.otel-healthcheck.tls=false"
      ]
    }

    service {
      name     = "jaeger-thrift"
      port     = "jaeger-thrift-http"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-jaeger-thrift.entrypoints=http,https",
        "traefik.http.routers.otel-jaeger-thrift.rule=Host(`otel-jaeger-thrift.obs.test.nsw.education`)",
        "traefik.http.routers.otel-jaeger-thrift.tls=false"
      ]
    }

    service {
       name = "otlphttp"
       port = "otlphttp"
       provider = "consul"

       tags = [
         "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.obs.test.nsw.education`)",
         "traefik.http.routers.otel-collector-http.entrypoints=http",
         "traefik.http.routers.otel-collector-http.tls=false",
         "traefik.enable=true",
       ]
    }

    service {
      name     = "pprof"
      port     = "pprof"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-pprof.entrypoints=http,https",
        "traefik.http.routers.otel-pprof.rule=Host(`otel.obs.test.nsw.education`) && Path(`/net/http/pprof`)",
        "traefik.http.routers.otel-pprof.tls=false"
      ]
    }

    task "otel-collector" {
      driver = "docker"
      env = {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
          type = "loki"
          config {
            loki-url = "https://otel.obs.test.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
        ports = [
        "otlphttp",
        "zipkin",
        "zpages",
        "healthcheck",
        "jaeger-grpc",
        "jaeger-thrift-http",
        "metrics",
        "otlp",
        "influxdb",
        "loki-receiver",
        "pprof"
        ]
      }

      resources {
        cpu    = 2000
        memory = 4092
      }

      template {
        data = <<EOF
receivers:
  otlp:
    protocols:
      http:
  jaeger:
    protocols:
      thrift_compact:
      thrift_binary:
      thrift_http:
  zipkin:
  opencensus:
  influxdb:
  loki:
    protocols:
      http:
    use_incoming_timestamp: true

  hostmetrics:
    scrapers:
      cpu:
      disk:
      filesystem:
      load:
      memory:
      network:
      process:
      processes:
      paging:
#   vcenter/silverwater:
#     endpoint: https://qa0991vivcr001.vi.uat.det.nsw.edu.au
#     tls: 
#       insecure: true
#     username: srv_obs-app-vcenter
#     password: GZgYIFO6E03PnHrZ9BFt
#     collection_interval: 2m

  vcenter/unanderra:
    endpoint: https://qa0992vivcr001.vi.uat.det.nsw.edu.au
    tls: 
      insecure: true
    username: srv_obs-app-vcenter
    password: GZgYIFO6E03PnHrZ9BFt
    collection_interval: 2m

#### Collect own metrics ####
  prometheus/collector:
    config:
      scrape_configs:
      - job_name: 'otel-collector'
        scrape_interval: 60s
        static_configs:
        - targets: ['0.0.0.0:8888']
        
  filelog:
    include: [/var/log/*.log]

processors:
  resourcedetection/system:
    # Modify the list of detectors to match the cloud environment
    detectors: [env, system, gcp, ec2, azure]
    timeout: 2s
    override: false

  attributes/env:
    actions:
      - key: environment
        action: upsert
        value: test
      - key: service_instance_id
        action: update
        value: "{{env "NOMAD_TASK_NAME"}}"

  attributes/loki:
    actions:
      - action: insert
        key: log_file_name
        from_attribute: log.file.name
      - action: insert
        key: loki.resource.labels
        value: service_name

  batch:
  memory_limiter:
    # 80% of maximum memory up to 2G
    limit_mib: 1500
    # 25% of limit up to 2G
    spike_limit_mib: 512
    check_interval: 5s

  probabilistic_sampler:
    sampling_percentage: 1

extensions:
### basic auth to grafanacloud OTLP gateway ###
  basicauth/otlp:
    client_auth:
      username: 533612
      password: eyJrIjoiYzc3MDQ2YjEzYjQ3NWY3OTU2ZmFiOTFiMDIzMzhiNGI3MGM1ODQ3YiIsIm4iOiJPVExQIiwiaWQiOjEzNjc1Nn0=

  health_check:
    endpoint: 0.0.0.0:13133
  pprof:
    endpoint: 0.0.0.0:1777
  memory_ballast:
    # Memory Ballast size should be max 1/3 to 1/2 of memory.
    size_mib: 683

exporters:
  logging:
    loglevel: error

### GrafanaCloud has a simple gateway ##
  otlphttp/grafanacloud:
    auth:
      authenticator: basicauth/otlp
    endpoint: https://otlp-gateway-prod-au-southeast-0.grafana.net/otlp

### mimir on-prem supports /otlp ingest using the otlphttp exporter, tenant is set with headers ###
  otlphttp/onpremmimir:
    endpoint: "https://mimir-rwb-write.obs.test.nsw.education/otlp"
    headers:
      X-Scope-ORGID: test

  loki/grafanacloud:
    endpoint: https://382995:********************************************************************************************************************************************@logs-prod-004.grafana.net/loki/api/v1/push
    

### tempo on-prem for jaeger traces ###
#  jaeger_thrift/onpremtempo:
#    endpoint: http://tempo-jaeger.obs.test.nsw.education/api/traces
#    tls:
#      insecure: true

service:
  extensions: [health_check,pprof,memory_ballast,basicauth/otlp]
  pipelines:
    traces/cloud:
      receivers: [otlp,jaeger,zipkin,opencensus]
      processors: [probabilistic_sampler,resourcedetection/system,attributes/env,memory_limiter,batch]
      exporters: [otlphttp/grafanacloud]

    metrics/cloud:
      receivers: [otlp,prometheus/collector,influxdb]
      processors: [resourcedetection/system,attributes/env,batch]
      exporters: [otlphttp/grafanacloud]

    metrics/local:
      receivers: [otlp,prometheus/collector,influxdb,vcenter/unanderra]
      processors: [resourcedetection/system,attributes/env,batch]
      exporters: [otlphttp/onpremmimir]

    logs/cloud:
      receivers: [filelog,otlp,loki]
      processors: [resourcedetection/system,attributes/env,attributes/loki]
      exporters: [logging,loki/grafanacloud]


  telemetry:
    logs:
      level: debug
      initial_fields:
        service: obscol-test
    metrics:
      address: "0.0.0.0:{{ env "NOMAD_PORT_metrics" }}"
      level: detailed
EOF

        change_mode   = "restart"
        destination   = "local/otel/config.yaml"
      }
    }
  }
}

// obs-col prod - prometheus-LTS.nomad job definition

// Long term storage instance - resides on obscol02
// to allow concurrent 'prometheus' to run.
// Refer Jedd or ROMEO-764     (2021-07)

job "prometheus-LTS" {
  type = "service"
  datacenters = ["dc-cir-un-prod"]

  group "prometheus" {

    network {
      port "prometheus" {
      static = 9090
      }
    }

    volume "prometheus"  {
      type = "host"
      source = "prometheus"
      read_only = false
      }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "pl0992obscol02.nsw.education"
    }

    task "prometheus" {
      driver = "docker"

      volume_mount {
        volume = "prometheus"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["prometheus"]
        image = "https://docker.io/prom/prometheus:v2.28.0"
        dns_servers = ["************"]
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        args = [ 
          "--storage.tsdb.retention.time=1y",
          "--config.file=/etc/prometheus/prometheus.yml",
          "--storage.tsdb.path=/prometheus", 
          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]
      }

      service {
        name = "prometheus-http"
        port = "prometheus"

        check {
          type = "http"
          port = "prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'nomad'
    metrics_path: /v1/metrics
    params:
      format: ['prometheus']
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['nomad-client', 'nomad']
        tags: ['http']

  # Any external node that runs telegraf
  - job_name: 'telegraf'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['telegraf']

  # Any external node that runs openmetrics (migrated from 'telegraf' 2021-07-30)
  - job_name: 'openmetrics'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['openmetrics']

  # Any standalone exporter that lives in the nomad cluster and not the agent
  - job_name: 'prometheus-exporter'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['prometheus-exporter']

  - job_name: 'nifi'
    metrics_path: /metrics/
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['nifi']

#    relabel_configs:
#      - source_labels: ['__meta_consul_tags']
#        regex: '(.*)http(.*)'
#        action: keep


EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 1500
        memory = 3000
      }

    }
  }
}

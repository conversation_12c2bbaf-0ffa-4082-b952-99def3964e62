job "test-jmx-jboss6" {
  datacenters = ["dev"]
  type = "service"

  group "collector" {
    count = 1

    task "jboss" {
      driver = "docker"

      config {
        image = "tutum/jboss:as6"

        port_map {
          jboss = 8080
          jboss_alt = 9990
        }
      }

      env {
        "JBOSS_PASS" = "zabbix"
      }

      resources {
        network {
          port "jboss" {}
        }
      }

      service {
        name = "jboss6-integration-test"
        port = "jboss"
      }
    }
  }
  # tutum/jboss:as6

}
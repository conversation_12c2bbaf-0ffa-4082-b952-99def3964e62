{"cells": [{"cell_type": "code", "execution_count": 209, "metadata": {}, "outputs": [], "source": ["# Importing libraries\n", "import requests\n", "import socket\n", "import yaml\n", "import json\n", "import pandas as pd\n", "import math\n", "import string\n", "from statistics import mode, StatisticsError"]}, {"cell_type": "code", "execution_count": 210, "metadata": {}, "outputs": [], "source": ["# Declare lists\n", "successful_requests = []\n", "failed_requests = []\n", "\n", "# Declare dictionaries\n", "json_payloads = {}\n", "json_payloads[f'Groups'] = {}\n", "grouped_app_ids = {}\n", "\n", "# Declare output filename for consul\n", "file_path = 'services.auto.tfvars.json'\n", "\n", "# Declare variables \n", "consul_url=\"http://tl0992obscol01.nsw.education:8500\"\n", "consul_token = 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'\n", "cirrus_access_token = 'MhwsJvhRQuPsuu6FVv6EDQnTlDuHAbmrsnC9h1oSyMpGwRkGsmpQNSGMpGsgLtMY'\n", "\n", "\n", "# Cirrus API and Filter \n", "cirrus_api_url = \"https://cirrus-api.nsw.education:3000/api/vms?filter=%7B%22fields%22%3A%20%7B%22os_manager%22%3A%22true%22%2C%22dns%22%3A%22true%22%2C%22app_id%22%3A%22true%22%2C%20%22app_id%22%3A%22true%22%2C%22dep_env%22%3A%22true%22%2C%22vm_loc%22%3A%22true%22%2C%22support_team%22%3A%22true%22%2C%22op_window%22%3A%22true%22%2C%22win_domain%22%3A%22true%22%2C%22hostname%22%3A%22true%22%7D%2C%20%22where%22%3A%20%7B%22active%22%3A%20%22true%22%2C%20%22vm_status%22%3A%22Active%22%7D%20%7D&access_token=\"\n", "\n", "# Cirrus API URL + Token\n", "api_url = f\"{cirrus_api_url}{cirrus_access_token}\""]}, {"cell_type": "code", "execution_count": 211, "metadata": {}, "outputs": [], "source": ["# Retrieve data from Cirrus API\n", "def get_hostnames_from_api(api_url):\n", "    try:\n", "        response = requests.get(api_url)\n", "        if response.status_code == 200:\n", "            data = response.json().get('data', [])\n", "            hostnames = [(\n", "                item.get('hostname'), \n", "                item.get('dns'), \n", "                item.get('dep_env'), \n", "                item.get('app_id'), \n", "                item.get('support_team'),\n", "                item.get('op_window'),\n", "                item.get('vm_loc'),\n", "                item.get('os_manager'),\n", "                item.get('win_domain')\n", "                ) \n", "                for item in data\n", "                ]\n", "            return hostnames\n", "        else:\n", "            print(\"Failed to fetch data from Cirrus\")\n", "            return []\n", "    except Exception as e:\n", "        print(\"Error:\", e)\n", "        return []"]}, {"cell_type": "code", "execution_count": 212, "metadata": {}, "outputs": [], "source": ["# Test service connection to hostnames\n", "def test_port_connection(fqdn, port):\n", "    try:\n", "        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:\n", "            s.settimeout(0.2)  \n", "            result = s.connect_ex((fqdn, port))\n", "            if result == 0:\n", "                return True\n", "            else:\n", "                return False\n", "    except Exception as e:\n", "        print(\"Error:\", e)\n", "        return False"]}, {"cell_type": "code", "execution_count": 213, "metadata": {}, "outputs": [], "source": ["# Test HTTP connection to hostnames tcp/11055\n", "def perform_http_request(fqdn, port, dep_env, app_id, support_team, op_window, vm_loc,manager, win_domain):\n", "    url = f\"http://{fqdn}:{port}/metrics\"\n", "    try:\n", "        response = requests.get(url)\n", "        if response.status_code == 200:\n", "            successful_requests.append({\n", "                'hostname': fqdn,\n", "                'dep_env': dep_env,\n", "                'app_id': app_id,\n", "                'support_team': support_team,\n", "                'op_window': op_window,\n", "                'vm_loc': vm_loc,\n", "                'os_manager' : manager,\n", "                'win_domain' : win_domain\n", "            })\n", "        else:\n", "            print(f\"HTTP request to {url} returned {response.status_code}\")\n", "    except Exception as e:\n", "        print(\"HTTP request error:\", e)"]}, {"cell_type": "code", "execution_count": 214, "metadata": {}, "outputs": [], "source": ["# Custom function to find the mode or handle ties\n", "def find_mode_or_first_common(values):\n", "    try:\n", "        # Try finding the mode using the standard library\n", "        return mode(values)\n", "    except StatisticsError:\n", "        # If there's a tie, return the first value among the most common values\n", "        if values:\n", "            return max(set(values), key=values.count)\n", "        else:\n", "            return None  # Or any default value you'd prefer"]}, {"cell_type": "code", "execution_count": 215, "metadata": {}, "outputs": [], "source": ["def custom_grouping(app_id):\n", "    first_letter = app_id[0].upper()\n", "    if first_letter in 'ABCDEFGHIJKLMNOPQRSTUV':  # A-V\n", "        return first_letter\n", "    else:  # Other cases\n", "        return 'W'"]}, {"cell_type": "code", "execution_count": 216, "metadata": {}, "outputs": [], "source": ["def create_dataframe(data):\n", "    \"\"\"Convert the list of successful requests into a DataFrame and preprocess it.\"\"\"\n", "    df = pd.DataFrame(data)\n", "    df.fillna('unset', inplace=True)\n", "    df['dep_env'] = df['dep_env'].str.replace('production', 'prod', case=False)\n", "    df['group'] = df['app_id'].apply(custom_grouping)\n", "    return df"]}, {"cell_type": "code", "execution_count": 217, "metadata": {}, "outputs": [], "source": ["def process_dataframe(df):\n", "    \"\"\"Process DataFrame to extract group metadata and generate payloads.\"\"\"\n", "    json_payloads = {'Groups': {}}\n", "    grouped_app_ids = {}\n", "\n", "    grouped = df.groupby(['app_id', 'dep_env', 'win_domain'])\n", "    for (app_id, dep_env, win_domain), group in grouped:\n", "        metadata, service_name = generate_metadata(group, app_id, dep_env, win_domain)\n", "        update_json_payloads(json_payloads, app_id, dep_env, win_domain, service_name, metadata, group)\n", "\n", "    for group_name, group in df.groupby('group'):\n", "        grouped_app_ids[group_name] = [f'openmetrics_{app_id}' for app_id in group['app_id'].drop_duplicates().tolist()]\n", "\n", "    return json_payloads, grouped_app_ids"]}, {"cell_type": "code", "execution_count": 218, "metadata": {}, "outputs": [], "source": ["def generate_metadata(group, app_id, dep_env, win_domain):\n", "    \"\"\"Generate metadata dictionary and service name from a group.\"\"\"\n", "    metadata = {\n", "        'env': dep_env.lower(),\n", "        'support_team': find_mode_or_first_common(group['support_team'].tolist()),\n", "        'op_window': find_mode_or_first_common(group['op_window'].tolist()),\n", "        'os_manager': find_mode_or_first_common(group['os_manager'].tolist()),\n", "        'app_id': app_id.lower(),\n", "        'cir_app_id': app_id.lower(),\n", "        'domain': win_domain.lower(),\n", "    }\n", "    service_name = f'openmetrics_{app_id}'\n", "    return metadata, service_name"]}, {"cell_type": "code", "execution_count": 219, "metadata": {}, "outputs": [], "source": ["def update_json_payloads(json_payloads, app_id, dep_env, win_domain, service_name, metadata, group):\n", "    \"\"\"Update the JSON payloads with new entries.\"\"\"\n", "    payload_key = f'{app_id}_{dep_env}_{win_domain}'\n", "    json_payloads['Groups'][payload_key] = {\n", "        'service_name': service_name,\n", "        'metrics_path': '/metrics',\n", "        'port': 11055,\n", "        'meta': metadata,\n", "        'nodes': group['hostname'].tolist()\n", "    }"]}, {"cell_type": "code", "execution_count": 220, "metadata": {}, "outputs": [], "source": ["def export_files(json_payloads, grouped_app_ids, yaml_filename=\"successful_requests.yaml\", json_filename=\"services.auto.tfvars.json\"):\n", "    \"\"\"Export data to YAML and JSON files.\"\"\"\n", "    with open(yaml_filename, \"w\") as yaml_file:\n", "        yaml.dump(successful_requests, yaml_file)\n", "    with open(json_filename, 'w') as json_file:\n", "        json.dump(json_payloads, json_file, indent=4)\n", "    for group_name, app_ids in grouped_app_ids.items():\n", "        with open(f'app_groups_{group_name}.json', 'w') as file:\n", "            json.dump(app_ids, file, indent=4)"]}, {"cell_type": "code", "execution_count": 221, "metadata": {}, "outputs": [], "source": ["\"\"\"\n", "\n", "The correct consul api path to use is /v1/catalog as it allows for service node and check registration with a single payload.\n", "\n", "\"\"\"\n", "\n", "def register_service_with_consul(service_payloads, consul_url, consul_token):\n", "    \"\"\"Register services and nodes with HashiCorp Consul.\n", "\n", "    Args:\n", "        service_payloads (dict): A dictionary containing the service payloads to register.\n", "        consul_url (str): The base URL for the Consul HTTP API.\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    headers = {\"Content-Type\": \"application/json\",\n", "               \"x-consul-token\": consul_token}     \n", "    for service_id, payload in service_payloads[\"Groups\"].items():\n", "        service_id = payload[\"service_name\"]\n", "        catalog_definition = {\n", "            #\"ID\": service_id,\n", "            \"Node\": payload[\"nodes\"][0],\n", "            \"Datacenter\":\"dc-cir-un-test\",\n", "            \"Address\": f'http://{payload[\"nodes\"][0]}',\n", "            \"Service\" : {\n", "                \"ID\": service_id,\n", "                # Removing tags from service definition, there will be obvious conflicts at this level.\n", "                # \"Tags\": [\n", "                #     payload[\"meta\"][\"env\"],\n", "                #     payload[\"meta\"][\"support_team\"],\n", "                #     payload[\"meta\"][\"op_window\"],\n", "                #     payload[\"meta\"][\"os_manager\"],\n", "                #     payload[\"meta\"][\"app_id\"],\n", "                #     payload[\"meta\"][\"cir_app_id\"],\n", "                #     payload[\"meta\"][\"domain\"],\n", "                #     ],\n", "                \"Port\": 11055,\n", "                \"Service\": f'{service_id}',\n", "                \"Address\": f'{payload[\"nodes\"][0]}',\n", "                \"TaggedAddresses\": {\n", "                    \"lan\" : {\n", "                        \"Address\": f'{payload[\"nodes\"][0]}',\n", "                        \"Port\": 11055\n", "                    }\n", "                },\n", "            },\n", "            \"Check\": {\n", "                \"CheckID\": f'service:{service_id}',\n", "                \"Status\": \"passing\",\n", "                \"ServiceID\": service_id,\n", "                \"Name\": f'OpenMetrics health check',\n", "                \"Definition\": {\n", "                    \"http\": f'http://{payload[\"nodes\"][0]}:11055/metrics',\n", "                    \"interval\": \"60s\",\n", "                    \"timeout\": \"5s\"\n", "                }\n", "            },\n", "            #\"SkipNodeUpdate\": True,\n", "        }\n", "        for node in payload[\"nodes\"]:\n", "            catalog_definition[\"Node\"] = node\n", "            catalog_definition[\"NodeMeta\"]  = {\n", "                        \"env\": payload[\"meta\"][\"env\"],\n", "                        \"support_team\": payload[\"meta\"][\"support_team\"],\n", "                        \"op_window\": payload[\"meta\"][\"op_window\"],\n", "                        \"os_manager\":payload[\"meta\"][\"os_manager\"],\n", "                        \"app_id\":payload[\"meta\"][\"app_id\"],\n", "                        \"cir_app_id\":payload[\"meta\"][\"cir_app_id\"],\n", "                        \"domain\":payload[\"meta\"][\"domain\"]        \n", "            }\n", "            #catalog_definition[\"Check\"][\"Node\"] = node\n", "            response = requests.put(f\"{consul_url}/v1/catalog/register\", headers=headers, data=json.dumps(catalog_definition))\n", "            if response.status_code == 200:\n", "                print(f\"Successfully registered {service_id} for node {node}\")\n", "            else:\n", "                print(f\"Failed to register {service_id} for node {node}. Response: {response.content}\")\n"]}, {"cell_type": "code", "execution_count": 222, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully registered openmetrics_aadc for node dw0472aadc0102.dev.nsw.education\n", "Successfully registered openmetrics_aadc for node dw0991aadc0101.dev.nsw.education\n", "Successfully registered openmetrics_aadc for node dw0992aadc0104.dev.nsw.education\n", "Successfully registered openmetrics_aadc for node dw0991aadc0301.dev.nsw.education\n", "Successfully registered openmetrics_aadc for node dw0992aadc0301.dev.nsw.education\n", "Successfully registered openmetrics_aadc for node qw0472aadc0102.pre.nsw.education\n", "Successfully registered openmetrics_aadc for node qw0991aadc0103.pre.nsw.education\n", "Successfully registered openmetrics_aadc for node qw0472aadc0101.pre.nsw.education\n", "Successfully registered openmetrics_aadc for node qw0992aadc0301.pre.nsw.education\n", "Successfully registered openmetrics_aadc for node qw0991aadc0301.pre.nsw.education\n", "Successfully registered openmetrics_aadc for node tw0472aadc0102.test.nsw.education\n", "Successfully registered openmetrics_aadc for node tw0992aadc0102.test.nsw.education\n", "Successfully registered openmetrics_aadc for node tw0472aadc0101.test.nsw.education\n", "Successfully registered openmetrics_aadc for node tw0992aadc0301.test.nsw.education\n", "Successfully registered openmetrics_aadc for node tw0991aadc0302.test.nsw.education\n", "Successfully registered openmetrics_aadc for node pw0991aadc0101.nsw.education\n", "Successfully registered openmetrics_aadc for node pw0992aadc0101.nsw.education\n", "Successfully registered openmetrics_aadc for node pw0472aadc0101.nsw.education\n", "Successfully registered openmetrics_aadc for node pw0992aadc0301.nsw.education\n", "Successfully registered openmetrics_aadc for node pw0991aadc0302.nsw.education\n", "Successfully registered openmetrics_aahw for node dw0992aahw0001.nsw.education\n", "Successfully registered openmetrics_aap for node tw0992aap00001.nsw.education\n", "Successfully registered openmetrics_aap for node pw0992aap00001.nsw.education\n", "Successfully registered openmetrics_aap for node pw0991aap00001.nsw.education\n", "Successfully registered openmetrics_acaa for node qu0992acaa001.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_acaa for node pu0992acaa001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_acaa for node pu0991acaa001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_access for node ql0992access04.nsw.education\n", "Successfully registered openmetrics_access for node ql0991access03.nsw.education\n", "Successfully registered openmetrics_access for node ql0991access04.nsw.education\n", "Successfully registered openmetrics_access for node ql0992access03.nsw.education\n", "Successfully registered openmetrics_access for node tl0991access03.nsw.education\n", "Successfully registered openmetrics_access for node pl0991access03.nsw.education\n", "Successfully registered openmetrics_access for node pl0992access03.nsw.education\n", "Successfully registered openmetrics_access for node pl0991access04.nsw.education\n", "Successfully registered openmetrics_access for node pl0992access04.nsw.education\n", "Successfully registered openmetrics_adcnb for node dw0991adcnb001.nsw.education\n", "Successfully registered openmetrics_adcnb for node dw0991adcnb002.nsw.education\n", "Successfully registered openmetrics_adcnb for node cw0991adcnb022.nsw.education\n", "Successfully registered openmetrics_adcnb for node dw0991adcnb003.nsw.education\n", "Successfully registered openmetrics_adcs for node dw0991cacr0102.nsw.education\n", "Successfully registered openmetrics_adcs for node tw0991cacr0102.test.nsw.education\n", "Successfully registered openmetrics_adcs for node pw0991cais0104.nsw.education\n", "Successfully registered openmetrics_addc for node pw0991rwdc0505.nsw.education\n", "Successfully registered openmetrics_addc for node pw0992rwdc0505.nsw.education\n", "Successfully registered openmetrics_adds for node dw0472rwdc0101.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0991adds0001.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0991rwdc0101.nsw.education\n", "Successfully registered openmetrics_adds for node dw0992rwdc0110.nsw.education\n", "Successfully registered openmetrics_adds for node dw0991rwdc0301.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0991rwdc0501.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0992rwdc0301.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0992rwdc0201.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0991rwdc0201.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0992rwdc0501.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0991rwdc0102.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0992rwdc0101.dev.nsw.education\n", "Successfully registered openmetrics_adds for node dw0992rwdc0102.dev.nsw.education\n", "Successfully registered openmetrics_adds for node qw0992rwdc0104.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0992rwdc0101.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0992rwdc0103.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0992rwdc0102.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0103.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0104.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0102.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0101.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0472rwdc0101.pre.nsw.education\n", "Successfully registered openmetrics_adds for node qw0992rwdc0302.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0992rwdc0301.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0302.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0301.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0992rwdc0201.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0992rwdc0202.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0201.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0202.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0992rwdc0501.messaging.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node qw0991rwdc0501.messaging.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0992rwdc0105.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0992rwdc0101.ad.test.mgmt.det\n", "Successfully registered openmetrics_adds for node tw0992rwdc0103.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0992rwdc0104.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0992rwdc0102.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0472rwdc0101.test.nsw.education\n", "Successfully registered openmetrics_adds for node tw0991rwdc0103.test.nsw.education\n", "Successfully registered openmetrics_adds for node tw0991rwdc0102.test.nsw.education\n", "Successfully registered openmetrics_adds for node tw0992rwdc0301.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0992rwdc0302.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0992rwdc0201.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0992rwdc0202.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0991rwdc0202.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0991rwdc0201.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0992rwdc0501.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0991rwdc0501.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_adds for node tw0991rwdc0301.test.nsw.education\n", "Successfully registered openmetrics_adds for node tw0991rwdc0203.test.nsw.education\n", "Successfully registered openmetrics_adds for node tw0991rwdc0502.test.nsw.education\n", "Successfully registered openmetrics_adds for node tw0991rwdc0101.test.nsw.education\n", "Successfully registered openmetrics_adds for node pw0472rwdc0102.nsw.education\n", "Successfully registered openmetrics_addsc for node pw0992rwdcx001.ad.mgmt.det\n", "Successfully registered openmetrics_adfs for node dw0992adfs0101.dev.nsw.education\n", "Successfully registered openmetrics_adfs for node dw0991adfs0102.dev.nsw.education\n", "Successfully registered openmetrics_adfs for node dw0991wap00101.dev.nsw.education\n", "Successfully registered openmetrics_adfs for node dw0992wap00101.dev.nsw.education\n", "Successfully registered openmetrics_adfs for node qw0991adfs0101.pre.nsw.education\n", "Successfully registered openmetrics_adfs for node qw0992adfs0101.pre.nsw.education\n", "Successfully registered openmetrics_adfs for node qw0992wap00101.pre.nsw.education\n", "Successfully registered openmetrics_adfs for node qw0991wap00101.pre.nsw.education\n", "Successfully registered openmetrics_adfs for node qw0991adfs0103.pre.nsw.education\n", "Successfully registered openmetrics_adfs for node qw0992adfs0103.pre.nsw.education\n", "Successfully registered openmetrics_adfs for node qw0991wap00102.nsw.education\n", "Successfully registered openmetrics_adfs for node qw0992wap00102.nsw.education\n", "Successfully registered openmetrics_adfs for node tw0991adfs0101.test.nsw.education\n", "Successfully registered openmetrics_adfs for node tw0992adfs0102.test.nsw.education\n", "Successfully registered openmetrics_adfs for node tw0991wap00101.test.nsw.education\n", "Successfully registered openmetrics_adfs for node tw0992wap00101.test.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991adfs0108.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991adfs0110.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991adfs0107.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991adfs0112.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991adfs0111.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991adfs0109.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992adfs0107.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992adfs0109.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992adfs0112.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992adfs0108.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992adfs0110.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992adfs0111.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991adfs0001.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991wap00107.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992wap00107.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991wap00108.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0991wap00109.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992wap00108.nsw.education\n", "Successfully registered openmetrics_adfs for node pw0992wap00109.nsw.education\n", "Successfully registered openmetrics_admim for node dw0991mim00101.dev.nsw.education\n", "Successfully registered openmetrics_admim for node tw0991mim00101.test.nsw.education\n", "Successfully registered openmetrics_admim for node pw0991mim00101.nsw.education\n", "Successfully registered openmetrics_admim for node pw0992mim00101.nsw.education\n", "Successfully registered openmetrics_admu for node dw0992util0101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_admu for node dw0991admu0101.dev.nsw.education\n", "Successfully registered openmetrics_admu for node dw0992admu0201.nsw.education\n", "Successfully registered openmetrics_admu for node dl0992admu0001.dev.nsw.education\n", "Successfully registered openmetrics_admu for node dw0991admu0301.dev.nsw.education\n", "Successfully registered openmetrics_admu for node dw0991admu0201.dev.nsw.education\n", "Successfully registered openmetrics_admu for node qw0992admu0001.pre.nsw.education\n", "Successfully registered openmetrics_admu for node qw0991admu0101.pre.nsw.education\n", "Successfully registered openmetrics_admu for node qw0992admu0101.pre.nsw.education\n", "Successfully registered openmetrics_admu for node tw0991admu0102.test.nsw.education\n", "Successfully registered openmetrics_admu for node tw0992admu0101.test.nsw.education\n", "Successfully registered openmetrics_admu for node tw0992admu0301.test.nsw.education\n", "Successfully registered openmetrics_admu for node pw0991adkp0101.nsw.education\n", "Successfully registered openmetrics_admu for node pw0991admu0105.nsw.education\n", "Successfully registered openmetrics_admu for node pw0991admo0101.nsw.education\n", "Successfully registered openmetrics_admu for node pw0472admu0101.nsw.education\n", "Successfully registered openmetrics_admu for node pw0991admu0201.nsw.education\n", "Successfully registered openmetrics_admu for node pw0992admu0201.nsw.education\n", "Successfully registered openmetrics_admu for node pw0992admu0301.pre.nsw.education\n", "Successfully registered openmetrics_admu for node pw0991admu0701.nsw.education\n", "Successfully registered openmetrics_adpoc for node tw0992adpoc001.nsw.education\n", "Successfully registered openmetrics_adrad for node pw0991adnps001.nsw.education\n", "Successfully registered openmetrics_adrad for node pw0992adnps002.nsw.education\n", "Successfully registered openmetrics_adrdl for node dw0992rdsl0101.dev.nsw.education\n", "Successfully registered openmetrics_adrdl for node dw0991rdsl0501.dev.nsw.education\n", "Successfully registered openmetrics_adrdl for node qw0992rdsl0101.pre.nsw.education\n", "Successfully registered openmetrics_adrdl for node tw0992rdsl0101.test.nsw.education\n", "Successfully registered openmetrics_adrdl for node tw0991rdsl0101.test.nsw.education\n", "Successfully registered openmetrics_adrdl for node tw0991rdsl0501.test.nsw.education\n", "Successfully registered openmetrics_adrdl for node pw0991rdsl0101.nsw.education\n", "Successfully registered openmetrics_adrdl for node pw0992rdsl0101.nsw.education\n", "Successfully registered openmetrics_adrdl for node pw0991adrdl001.nsw.education\n", "Successfully registered openmetrics_amfa for node tw0991amfa0102.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_amfa for node tw0991amfa0101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node dw0000amswaa01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node dw0000amsags01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node dw0000amsowl01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node qw0000amsags01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node qw0000amsowl01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node qw0000amswaa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node tw0000amsags01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node tw0000amsowl01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node tw0000amswaa01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node pw0000amswaa01.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node pw0000amsags01.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node pw0000amsowl01.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_amso for node pw0000amsowl03.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_ansb for node pu0992ansb0001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ansb for node pu0991ansb0001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_antt for node dl0475ant0216a.nsw.education\n", "Successfully registered openmetrics_arap for node pw0992arap0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_astp for node dw0000astpap01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_astp for node pw0000astpwts2.nsw.education\n", "Successfully registered openmetrics_astp for node pw0000astpcrs2.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_auaa for node pw0000auaap001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_auaa for node pw0000auaap002.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_aufm for node pw0992aufm0001.nsw.education\n", "Successfully registered openmetrics_aurora for node pw0991aurora01.nsw.education\n", "Successfully registered openmetrics_awsi for node pw0991awsi0001.nsw.education\n", "Successfully registered openmetrics_azmig for node tw0992azmig004.test.nsw.education\n", "Successfully registered openmetrics_azmig for node tw0991azmig004.test.nsw.education\n", "Successfully registered openmetrics_azmig for node pw0992azmig004.nsw.education\n", "Successfully registered openmetrics_azmig for node pw0991azmig004.nsw.education\n", "Successfully registered openmetrics_azmig for node pw0992azmig001.nsw.education\n", "Successfully registered openmetrics_b000 for node pw0000cmz00003.hbm.mgmt.det\n", "Successfully registered openmetrics_b000 for node pw0000cmz00004.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_b000 for node pw0000cmz00001.hbm.mgmt.det\n", "Successfully registered openmetrics_bamboo for node dl0992bamboo03.nsw.education\n", "Successfully registered openmetrics_bamboo for node dl0992bamboo04.nsw.education\n", "Successfully registered openmetrics_bamboo for node dl0992bamboo02.nsw.education\n", "Successfully registered openmetrics_bamboo for node dl0992bamboo01.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0991bamboo00.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0991bamboo01.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0992bamboo04.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0992bamboo06.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0992bamboo05.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0991bamboo03.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0991bamboo02.nsw.education\n", "Successfully registered openmetrics_bamboo for node dl0992bamboo00.nsw.education\n", "Successfully registered openmetrics_bamboo for node dl0992bamboo22.nsw.education\n", "Successfully registered openmetrics_bamboo for node dl0992bamboo11.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0991bamboo11.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0991bamboo12.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0992bamboo00.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0475bamboo09.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0475bamboo10.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0475bamboo07.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0475bamboo08.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0992bamboo08.nsw.education\n", "Successfully registered openmetrics_bamboo for node pl0475bamboo11.nsw.education\n", "Successfully registered openmetrics_bamtib for node pl0992bamtib01.nsw.education\n", "Successfully registered openmetrics_bl00 for node utvdccbl001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_bl00 for node pw0000csapp001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_brik for node ps0475brik1003.nsw.education\n", "Successfully registered openmetrics_brik for node ps0475brik1001.nsw.education\n", "Successfully registered openmetrics_brik for node ps0475brik1002.nsw.education\n", "Successfully registered openmetrics_brik for node ps0475brik1004.nsw.education\n", "Successfully registered openmetrics_brik for node ps0475brik1006.nsw.education\n", "Successfully registered openmetrics_brik for node ps0475brik1005.nsw.education\n", "Successfully registered openmetrics_brmd for node pu0000brmd0001.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_bucket for node pl0992bucket02.nsw.education\n", "Successfully registered openmetrics_bucket for node pl0992bucket01.nsw.education\n", "Successfully registered openmetrics_bucket for node pl0992bucket03.nsw.education\n", "Successfully registered openmetrics_bucket for node pl0992bucket05.nsw.education\n", "Successfully registered openmetrics_bucket for node pl0992bucket04.nsw.education\n", "Successfully registered openmetrics_bucket for node pl0992bucket06.nsw.education\n", "Successfully registered openmetrics_budapp for node tu0000netprt01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_budapp for node tl0475budapp01.nsw.education\n", "Successfully registered openmetrics_budapp for node pu0000netprt01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_bworks for node dl0991bworks01.nsw.education\n", "Successfully registered openmetrics_bworks for node ql0991bworks01.nsw.education\n", "Successfully registered openmetrics_bworks for node ql0992bworks01.nsw.education\n", "Successfully registered openmetrics_bworks for node ql0992bworks10.nsw.education\n", "Successfully registered openmetrics_bworks for node tl0992bworks01.nsw.education\n", "Successfully registered openmetrics_bworks for node tl0992bworks02.nsw.education\n", "Successfully registered openmetrics_bworks for node pl0992bworks01.nsw.education\n", "Successfully registered openmetrics_bworks for node pl0991bworks01.nsw.education\n", "Successfully registered openmetrics_cain for node pw0991cain9902.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cash for node pw0992cash0103.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_cash for node pw0991cash0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_cash8d for node pw0991cash0102.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_cdm for node dw0472cdm00002.nsw.education\n", "Successfully registered openmetrics_cdm for node pw0472cdm00002.nsw.education\n", "Successfully registered openmetrics_cdm for node pw0472cdm00001.nsw.education\n", "Successfully registered openmetrics_cedrms for node dw0992crmswg01.dev.nsw.education\n", "Successfully registered openmetrics_cedrms for node qw0992crmswg01.pre.nsw.education\n", "Successfully registered openmetrics_cedrms for node qw0992crmswg02.pre.nsw.education\n", "Successfully registered openmetrics_cedrms for node qw0992crmsev01.pre.nsw.education\n", "Successfully registered openmetrics_cedrms for node qw0992crmsid01.pre.nsw.education\n", "Successfully registered openmetrics_cedrms for node qw0991crmswg01.pre.nsw.education\n", "Successfully registered openmetrics_cedrms for node qw0991crmsev01.pre.nsw.education\n", "Successfully registered openmetrics_cedrms for node qw0992crmsid02.pre.nsw.education\n", "Successfully registered openmetrics_cedrms for node qw0992crmswg03.pre.nsw.education\n", "Successfully registered openmetrics_cedrms for node tw0992crmswg01.test.nsw.education\n", "Successfully registered openmetrics_cedrms for node tw0992crmswg02.test.nsw.education\n", "Successfully registered openmetrics_cedrms for node ew0992crmswg01.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0992crmsev01.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0992crmswg03.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0992crmswg01.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0992crmsid01.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0992crmswg02.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0992crmswg04.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0992crmsid02.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0991crmswg04.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0991crmsev01.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0991crmswg02.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0991crmswg03.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0991crmswg01.nsw.education\n", "Successfully registered openmetrics_cedrms for node pw0992crmswg05.nsw.education\n", "Successfully registered openmetrics_cesd for node pw0000cesdbi02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cesd for node pw0000cesdbi01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cesd for node pw0000cesdbi04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cesd for node pw0000cesdbi03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cese for node pw0992cesehs01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cese for node pw0992ceseis01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cese37 for node pw0992ceseap01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_ch00 for node udvacapp002.multimedia.test.det.nsw.edu.au\n", "Successfully registered openmetrics_ch00 for node udvacapp001.multimedia.test.det.nsw.edu.au\n", "Successfully registered openmetrics_ch00 for node qw0000acsch001.multimedia.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_ch00 for node qw0000acsch002.multimedia.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_ch00 for node upvacsch001.multimedia.det.nsw.edu.au\n", "Successfully registered openmetrics_ch00 for node upvacsch002.multimedia.det.nsw.edu.au\n", "Successfully registered openmetrics_ch00 for node upvacsch004.multimedia.det.nsw.edu.au\n", "Successfully registered openmetrics_ch00 for node upvacsch003.multimedia.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node wdcm12b00007.apps.test.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node wdcm12b00006.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node wdcm12srs001.apps.test.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node wdcm12h01001.apps.test.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node wdcm12b00001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node wdcm12b00002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node wdcm12a00001.apps.test.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00002.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00009.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cml00004.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmj00001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00006.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00004.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmp00005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmq00005.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00009.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmk00003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmm00005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00004.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00007.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh02001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmm00001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cml00005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh02005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmn00006.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cml00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00008.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00008.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmq00004.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00007.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00009.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00008.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmn00003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00007.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmm00003.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00002.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmj00004.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh01004.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmj00005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmj00003.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh01002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00004.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh01001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmq00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmn00007.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh02004.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmk00005.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00008.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmn00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmk00007.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmq00003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00004.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00005.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmk00004.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmk00006.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00009.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00008.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cml00003.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00003.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmj00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmm00006.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00003.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00006.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmp00003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh01005.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmn00004.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00006.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00006.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmn00005.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmp00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00007.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmq00006.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh02003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh01003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cml00006.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cma00001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmp00004.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmn00001.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cml00007.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmk00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh02002.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00001.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmm00004.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00007.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmd00006.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmb00004.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmp00007.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmm00007.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmq00001.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmq00007.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmp00006.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmm00002.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmc00003.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmp00001.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cmh00001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmj00007.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cme00009.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmj00006.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmh0 for node pw0000cml00001.appu.mgmt.det\n", "Successfully registered openmetrics_cmh0 for node pw0000cmk00001.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node dw0000cmy00003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node dw0000cmy00006.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node dw0000cmy00007.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node dw0000cmy00002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node dw0000cmy00001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node pw0000cmy00006.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node pw0000cmy00007.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node pw0000cmy00001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node pw0000cmy00002.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cmy0 for node pw0000cmy00003.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_cnsl for node dl0992cnsl0001.nsw.education\n", "Successfully registered openmetrics_cnsl for node dl0992cnsl0003.nsw.education\n", "Successfully registered openmetrics_cnsl for node dl0992cnsl0002.nsw.education\n", "Successfully registered openmetrics_cnsl for node pl0475cnsl0001.nsw.education\n", "Successfully registered openmetrics_cnsl for node pl0475cnsl0002.nsw.education\n", "Successfully registered openmetrics_cnsl for node pl0475cnsl0003.nsw.education\n", "Successfully registered openmetrics_cnsl for node pl0475cnsl0004.nsw.education\n", "Successfully registered openmetrics_cnsl for node pl0475cnsl0005.nsw.education\n", "Successfully registered openmetrics_conflu for node pl0475conflu07.nsw.education\n", "Successfully registered openmetrics_conflu for node pl0475conflu06.nsw.education\n", "Successfully registered openmetrics_conflu for node pl0475conflu03.nsw.education\n", "Successfully registered openmetrics_conflu for node pl0475conflu01.nsw.education\n", "Successfully registered openmetrics_conflu for node pl0475conflu02.nsw.education\n", "Successfully registered openmetrics_coutl for node dl0475coutl001.nsw.education\n", "Successfully registered openmetrics_coutl for node dl0475coutl002.nsw.education\n", "Successfully registered openmetrics_coutl for node pw0991coutl001.nsw.education\n", "Successfully registered openmetrics_coutl for node pl0475coutl001.nsw.education\n", "Successfully registered openmetrics_coutl for node pl0475coutl002.nsw.education\n", "Successfully registered openmetrics_coutl for node pl0475coutl003.nsw.education\n", "Successfully registered openmetrics_cport for node dl0991cport001.nsw.education\n", "Successfully registered openmetrics_cport for node dl0991cport002.nsw.education\n", "Successfully registered openmetrics_cprov for node dl0991cprovlb1.nsw.education\n", "Successfully registered openmetrics_cprov for node dl0991cprovlb2.nsw.education\n", "Successfully registered openmetrics_cprov for node dl0991cprov007.nsw.education\n", "Successfully registered openmetrics_cprov for node dl0991cprov008.nsw.education\n", "Successfully registered openmetrics_cprov for node dl0991cprov009.nsw.education\n", "Successfully registered openmetrics_cprov for node dl0991cprovd02.nsw.education\n", "Successfully registered openmetrics_cprov for node pw0991cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node pw0992cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node pw0991cprov002.nsw.education\n", "Successfully registered openmetrics_cprov for node pw0992cprov002.nsw.education\n", "Successfully registered openmetrics_cprov for node dw0992cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node dw0991cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node qw0992cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node qw0991cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node tw0991cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node tw0992cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node tl0991cprov001.nsw.education\n", "Successfully registered openmetrics_cprov for node tl0991cprov002.nsw.education\n", "Successfully registered openmetrics_cprov for node tl0991cprov003.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprovlb2.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprovlb1.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprov007.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprov008.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprov009.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprov010.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprov011.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprov012.nsw.education\n", "Successfully registered openmetrics_cprov for node pl0991cprovd02.nsw.education\n", "Successfully registered openmetrics_csndra for node tl0991csndra01.nsw.education\n", "Successfully registered openmetrics_cybpam for node tw0991cybpam01.test.nsw.education\n", "Successfully registered openmetrics_cybpam for node tw0992cybpam01.test.nsw.education\n", "Successfully registered openmetrics_cybpam for node tw0991cybpam02.nsw.education\n", "Successfully registered openmetrics_cybpam for node tw0992cybpam03.test.nsw.education\n", "Successfully registered openmetrics_cybpam for node tw0991cybpam03.test.nsw.education\n", "Successfully registered openmetrics_cybpam for node tl0991cybpam01.nsw.education\n", "Successfully registered openmetrics_cybpam for node tl0991cybtst02.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0991cybpam01.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0992cybpam01.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0991cybpam02.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0991cybpam03.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0992cybpam02.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0992cybpam03.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0991sappxy01.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0992sappxy01.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0992cybpams1.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0991cybpams1.nsw.education\n", "Successfully registered openmetrics_cybpam for node pw0992cybpam04.nsw.education\n", "Successfully registered openmetrics_daikon for node dw0992daikon01.nsw.education\n", "Successfully registered openmetrics_daikon for node dw0991daikon01.nsw.education\n", "Successfully registered openmetrics_daikon for node qw0991daikon01.nsw.education\n", "Successfully registered openmetrics_daikon for node qw0991daikon02.nsw.education\n", "Successfully registered openmetrics_daikon for node qw0992daikon01.nsw.education\n", "Successfully registered openmetrics_daikon for node qw0992daikon02.nsw.education\n", "Successfully registered openmetrics_daikon for node qw0992daikon03.nsw.education\n", "Successfully registered openmetrics_daikon for node qw0991daikon03.nsw.education\n", "Successfully registered openmetrics_daikon for node tw0991daikon01.nsw.education\n", "Successfully registered openmetrics_daikon for node tw0992daikon01.nsw.education\n", "Successfully registered openmetrics_daikon for node pw0991daikon01.nsw.education\n", "Successfully registered openmetrics_daikon for node pw0991daikon02.nsw.education\n", "Successfully registered openmetrics_daikon for node pw0992daikon02.nsw.education\n", "Successfully registered openmetrics_daikon for node pw0992daikon01.nsw.education\n", "Successfully registered openmetrics_daikon for node pw0991daikon03.nsw.education\n", "Successfully registered openmetrics_daikon for node pw0992daikon03.nsw.education\n", "Successfully registered openmetrics_dds for node dw0992dds00001.dev.nsw.education\n", "Successfully registered openmetrics_dds0 for node uw0000dds0002.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_dds0 for node uw0000dds0001.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_dds0 for node pw0000dds0002.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_dds0 for node pw0000dds0001.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node dw0000deiapp01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node dw0000deiweb01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node dw0000deiweb02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node dw0000deiapp04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node dw0000deiapp02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node dw0000deiapp03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node qw0000deiapp04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node qw0000deiapp01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node qw0000deiweb01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node qw0000deiweb02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node qw0000deiapp02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node qw0000deiapp03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node tw0000deiapp01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node tw0000deiapp04.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node tw0000deiweb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node tw0000deiweb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node tw0000deiapp03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node tw0000deiapp02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node pw0000deiapp04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node pw0000deiapp02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node pw0000deiapp03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node pw0000deiweb02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node pw0000deiapp01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node pw0000deiweb01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node ew0000deiapp01.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node ew0000deiweb01.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node ew0000deiapp02.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_deia for node ew0000deiweb02.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_deifmp for node pw0472deifmp01.nsw.education\n", "Successfully registered openmetrics_dera for node pw0472dera0001.nsw.education\n", "Successfully registered openmetrics_dfsn for node dw0472dfsn0001.dev.nsw.education\n", "Successfully registered openmetrics_dfsn for node tw0472dfsn0001.test.nsw.education\n", "Successfully registered openmetrics_dfsn for node pw0472dfsn0001.nsw.education\n", "Successfully registered openmetrics_dfsn for node pw0472dfsn0002.nsw.education\n", "Successfully registered openmetrics_dfsn for node pw0472dfsn0003.nsw.education\n", "Successfully registered openmetrics_dfsn for node pw0472dfsn0004.nsw.education\n", "Successfully registered openmetrics_dpki for node dw0991dpki0101.nsw.education\n", "Successfully registered openmetrics_dpki for node tw0991dpki0101.nsw.education\n", "Successfully registered openmetrics_dutil for node dw0992util0601.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0018.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsi0004.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4rcr0004.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4rcr0002.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4lpr0008.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0014.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0006.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agi0008.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0004.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0010.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0002.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agi0002.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0002.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsi0002.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0004.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4rcr0006.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0006.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4lpr0004.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0006.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsi0008.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsi0006.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4lpr0002.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0008.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agi0004.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4lpr0006.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0002.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4rcr0008.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agi0006.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0012.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0020.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0008.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0010.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0008.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0016.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0004.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0024.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0006.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0028.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0026.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0012.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0014.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0014.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0010.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0002.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0022.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0016.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0008.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0004.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0012.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsi0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw00053.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsi0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0009.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0013.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0009.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsi0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4lpr0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0013.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0011.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0017.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0019.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0021.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0011.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4lpr0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0013.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0023.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0015.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4lpr0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4rcr0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0019.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4rcr0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0017.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0015.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0019.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0011.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4rcr0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4rcr0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0017.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4lpr0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4sws0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsi0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0027.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agw0025.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agi0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agi0009.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0011.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0013.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0015.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0015.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0011.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agi0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0011.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0003.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wst0009.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0015.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4agi0005.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0009.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0013.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4ags0007.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wsf0009.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0009.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ag25 for node pw0000e4wss0013.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4se for node tw0000e4sed001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4se48 for node tw0000e4sedb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4se4e for node pw0991e4sed001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4se79 for node tw0000e4seda01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4se97 for node qw0000e4sedz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4se98 for node qw0000e4sed001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr55 for node dw0000e4srsb20.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr56 for node dw0000e4srsa20.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0038.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0028.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0034.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0036.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0024.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0022.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0026.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0020.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0030.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0032.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0023.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0027.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0025.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0021.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0037.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0035.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0029.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0033.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0039.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr6a for node pw0000e4srs0031.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr9b for node xw0000e4srs0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr9b for node xw0000e4srs0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr9d for node ew0000e4srs0021.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr9d for node ew0000e4srs0020.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4sr9d for node ew0000e4srsa20.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node qw0000e4uisz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node qw0000e4uisz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node qw0000e4uisz07.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node qw0000e4uisz08.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node qw0000e4uisz09.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node pw0000e4uis0001.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node pw0000e4uis0009.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node pw0000e4uis0011.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node pw0000e4uis0013.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui for node pw0000e4uis0015.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui50 for node qw0000e4uis0005.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui50 for node qw0000e4uis0006.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui50 for node qw0000e4uis0007.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui50 for node qw0000e4uis0008.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui9c for node xw0000e4uis0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ui9c for node xw0000e4uis0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz19.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agsz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agsz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz22.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agsz07.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4swsz08.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4lprz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agsz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agsz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4swsz07.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4swsz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz18.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz17.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agiz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agsz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agsz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4swsz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsiz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz15.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4rcrz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4lprz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz20.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4lprz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsiz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz16.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz13.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz29.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wstz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz12.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4rcrz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz07.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz07.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4lprz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wstz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsiz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz09.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wstz08.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wstz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz21.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wstz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4lprz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wstz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsiz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4rcrz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agiz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4swsz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4swsz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz09.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz20.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz28.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4rcrz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agiz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz03.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz23.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4rcrz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wstz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wstz07.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz10.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz26.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4rcrz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz25.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agiz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz08.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz01.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz05.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz08.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz09.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz10.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4swsz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz11.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsiz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz08.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz10.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz14.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4lprz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz04.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wsfz07.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz24.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4wssz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4agwz06.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4srsz27.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws44 for node qw0000e4swsz02.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4uisa03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4lprb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agsb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agwa02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4srsb20.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agsb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agsa02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4swsa02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wstb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agsa01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsfb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsia02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wssa02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4srsa21.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4srsa01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4rcra02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4srsa02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4swsa01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4rcrb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4lprb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4swsb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agwa01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agia02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agwb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4swsb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agib01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4lpra02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wssa01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4srsa20.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4srsb21.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agwb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wssb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wssb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4rcra01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wstb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsta01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsfb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsta02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4rcrb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsib01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4lpra01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agib02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsib02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsia01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsfa02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4agia01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws46 for node tw0000e4wsfa01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4rcr0007.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0024.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0018.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0026.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0028.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0018.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0021.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4lpr0007.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4lpr0009.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0018.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0015.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0014.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0019.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0008.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0016.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0022.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0012.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0018.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0014.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4lpr0008.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0022.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0016.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4rcr0008.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0011.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0030.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0017.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0011.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4rcr0006.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agi0009.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0014.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0017.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0006.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0017.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0012.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0023.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0009.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agi0007.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0016.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0019.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0019.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0020.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0016.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0014.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agi0006.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0011.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0013.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0015.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0012.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0018.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0025.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0013.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0024.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0029.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0014.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0013.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0031.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0017.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0016.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0032.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0012.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0013.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0010.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0017.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0016.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0012.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0013.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0014.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4lpr0006.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0007.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0027.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0011.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4rcr0009.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0011.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0015.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0011.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0015.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0012.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0020.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0018.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0033.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0023.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0020.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0015.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0017.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agi0008.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0013.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0015.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0011.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0012.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0021.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0008.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4lpr0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4rcr0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wst0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4srs0020.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agi0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0010.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0009.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0006.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0008.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0006.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0003.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0005.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0007.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0007.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0010.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0004.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0009.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0005.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0004.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agi0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4lpr0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0005.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4sws0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0004.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wss0003.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0003.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsf0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4ags0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4rcr0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4wsi0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws4d for node qw0000e4agw0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4wst002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4wst001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4ags0002ntservices.xdev.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4agi0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4wss0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4wsf0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4wsi0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4lpr0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4agw0001ntweb.xdev.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4sws0002almintegration.xdev.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4rcr0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4agw0002ntweb.xdev.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4sws0001almintegration.xdev.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4lpr0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4rcr0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4wss0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4wsf0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4agi0001ntegrationagent.xdev.ebs.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4wsi0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws6b for node xw0000e4ags0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4lpr0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4wss0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4wsi0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4sws0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4wst002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4agi0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4rcr0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4ags0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4ags0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4wst001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4agi0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4wsi0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4wsf0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4agw0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4lpr0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4wsf0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4agw0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4sws0002.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4rcr0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws74 for node ew0000e4wss0001.ebs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4ags003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agt003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4lrcr001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agi002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4lrcr004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4uis003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wsf004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agt004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agi003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4ags004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agt002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4lpr004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wsi001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4lpr003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4srs020.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agw003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wss002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wss003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wsi004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wsf003_temp.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4lpr002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agi001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wss004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4lpr001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wst002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agt001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agi004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4lrcr002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wst003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wst001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agw001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4sws002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4ags001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agw004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4sws001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4ags002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wsf002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4agw002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wsi002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4lrcr003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wst004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4sws004.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wsi003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wss001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4srs021.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4wsf001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_e4ws75 for node tw0000e4sws003.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_ebsetl for node pw0991ebsetl01.nsw.education\n", "Successfully registered openmetrics_ebsx for node dw0992ebsx0001.nsw.education\n", "Successfully registered openmetrics_ebsx for node dw0992ebsx0002.nsw.education\n", "Successfully registered openmetrics_echous for node pw0992viutl001.vi.det.nsw.edu.au\n", "Successfully registered openmetrics_echous for node pw0991viutl001.vi.det.nsw.edu.au\n", "Successfully registered openmetrics_elake for node dw0472elakeir1.nsw.education\n", "Successfully registered openmetrics_et00 for node upvewnet001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_et4lus for node dw0000cmxxutl1.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_et4lus for node pw0992cmxxutl01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_et4lus for node pw0000cmxxutl3.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_et4lus for node pw0991cmxxutl01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_etla for node dl0992etla7001.nsw.education\n", "Successfully registered openmetrics_etla for node tl0992etla7001.nsw.education\n", "Successfully registered openmetrics_etla for node pl0992etla7001.nsw.education\n", "Successfully registered openmetrics_evnt for node tw0992evnt0102.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_evnt for node tw0992evnt0101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_evnt for node tw0991evnt0102.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_evnt for node tw0991evnt0101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_evnt2b for node pw0992evnt0101.detnsw.win\n", "Successfully registered openmetrics_evnt2b for node pw0991evnt0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_ewdb for node dw0000ewlapoc1.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_ewdb for node qw0000ewlapoc1.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_ewdb for node qw0000ewdbpoc1.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_ewdb for node pw0000ewdbpoc1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_ewdb for node pw0000ewlapoc1.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_ewjh for node tw0000sapfts01.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_ewjh for node tw0000sappoc01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_ewjh for node pw0992hanats01.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_ewjh for node pw0991hanats01.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node dw0992ewmgt001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node dw0991ewmgt001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node dw0992ewmgt501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node dw0991ewmgt501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node qw0992ewmgt001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node qw0991ewmgt001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node qw0992ewmgt501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node qw0991ewmgt501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node pw0991ewda0001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node tw0992ewmgt001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node tw0991ewmgt001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node tw0992ewmgt501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node tw0991ewmgt501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node pw0992ewmgt001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node pw0991ewmgt001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node pw0992ewmgt501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ewmg for node pw0991ewmgt501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ex13 for node tw0992exadmn02.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_ex13 for node tw0991exadmn01.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_ex13 for node pw0992ex13eac02.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex13 for node pw0991ex13eac01.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex132d for node pw0992ex13r102.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex132d for node pw0991ex13r101.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex19 for node dw0991ex19ex01.dev.nsw.education\n", "Successfully registered openmetrics_ex19 for node dw0991ex19fs01.dev.nsw.education\n", "Successfully registered openmetrics_ex19 for node dw0992ex19fs02.dev.nsw.education\n", "Successfully registered openmetrics_ex19 for node dw0992ex19ex02.dev.nsw.education\n", "Successfully registered openmetrics_ex19 for node tw0991ex19ex01.test.nsw.education\n", "Successfully registered openmetrics_ex19 for node tw0991ex19fs01.test.nsw.education\n", "Successfully registered openmetrics_ex19 for node tw0992ex19ex02.test.nsw.education\n", "Successfully registered openmetrics_ex19 for node tw0992ex19fs02.test.nsw.education\n", "Successfully registered openmetrics_ex19 for node tw0992ex19fs04.test.nsw.education\n", "Successfully registered openmetrics_ex19 for node tw0991ex19fs03.test.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0992ex19ex04.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0991ex19fs01.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0992ex19fs02.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0991ex19fs03.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0992ex19fs04.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0991ex19ex01.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0991ex19ex03.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0992ex19ex02.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0991ex19fs05.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0992ex19fs06.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0991exedge02.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex19 for node pw0991ex19fs07.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0992ex19fs08.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0991ex19stel.nsw.education\n", "Successfully registered openmetrics_ex19 for node pw0991exedge03.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex19 for node pw0991exedge01.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex19 for node pw0992exedge01.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex19 for node pw0992exedge02.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_ex19 for node pw0992exedge03.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_exar for node pw0992exarap01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_exmm71 for node pw0991exmmam01.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_exps for node tw0472exps0001.nsw.education\n", "Successfully registered openmetrics_exut for node dw0992exutil06.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_exut for node dw0991exutil05.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_exut for node tw0992exutil06.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_exut for node tw0991exutil05.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_exutil for node pw0991exadm03.nsw.education\n", "Successfully registered openmetrics_exutil for node pw0992exadm04.nsw.education\n", "Successfully registered openmetrics_file for node pw0991file0002.nsw.education\n", "Successfully registered openmetrics_file for node pw0991file0001.nsw.education\n", "Successfully registered openmetrics_finops for node pl0475finops01.nsw.education\n", "Successfully registered openmetrics_fmar for node pw0000fmars001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000445fs02.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000202fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000238fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000210fs02.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000824fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000236fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000218fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000239fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000830fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00009565fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000237fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000205fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000210fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000828fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000226fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000230fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000381fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000239fs02.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000445fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fopsts for node pw00000999fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_fort for node pw0991fort0001.nsw.education\n", "Successfully registered openmetrics_fstftp for node pw0472fstftp01.nsw.education\n", "Successfully registered openmetrics_fxdm for node pw0992fxdm0004.nsw.education\n", "Successfully registered openmetrics_fxdm for node pw0992fxdm0001.nsw.education\n", "Successfully registered openmetrics_fxdm for node pw0992fxdm0005.nsw.education\n", "Successfully registered openmetrics_fxdm for node pw0992fxdm0002.nsw.education\n", "Successfully registered openmetrics_fxdm for node pw0992fxdm0003.nsw.education\n", "Successfully registered openmetrics_fxdm for node pw0992fxdm0006.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0472fxfmpa01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0472fxfmpa02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0472fxfmpo01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0472fxfmpo02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0472fxfmpp01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0472fxfmpp02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0472fxfmps01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0991fxfmpa01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0991fxfmpp01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0991fxfmpo02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0992fxfmpo01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0991fxfmpo01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0991fxfmpp02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0992fxfmpa01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0992fxfmpp01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0992fxfmpo02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0991fxfmps01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0992fxfmpp02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node qw0992fxfmps01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node tw0991fxfmpa01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node tw0991fxfmpo01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node tw0991fxfmpp01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node tw0991fxfmps01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0991fxfmpo02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0991fxfmpa01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0991fxfmpp01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0991fxfmpo01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0991fxfmpp02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0992fxfmpo02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0991fxfmpp03.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0992fxfmpo01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0992fxfmpa01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0992fxfmpp01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0992fxfmpp03.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0992fxfmpp02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0992fxfmps01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0991fxfmps01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0472fxfmpa01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0472fxfmpa02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0472fxfmpp01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0472fxfmpp02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0472fxfmpo01.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0472fxfmpo02.nsw.education\n", "Successfully registered openmetrics_fxfmp for node pw0472fxfmps01.nsw.education\n", "Successfully registered openmetrics_guains for node pw0472guains01.nsw.education\n", "Successfully registered openmetrics_hbase for node dl0991hdpj2001.nsw.education\n", "Successfully registered openmetrics_hbase for node dl0991hdpe2001.nsw.education\n", "Successfully registered openmetrics_hbase for node dl0991hdpd2001.nsw.education\n", "Successfully registered openmetrics_hbase for node dl0991hdpsq001.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0991hdpsq001.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0992hdpsq001.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0992hdpj2701.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0992hdpj2702.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0992hdpe2701.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0992hdpd2701.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0992hdpd2702.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0992hdpd2703.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0992hdpd2704.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0991hdpd1701.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0991hdpe1701.nsw.education\n", "Successfully registered openmetrics_hbase for node ql0991hdpj1701.nsw.education\n", "Successfully registered openmetrics_hbase for node tl0992hdpsq001.nsw.education\n", "Successfully registered openmetrics_hbase for node tl0992hdpj2701.nsw.education\n", "Successfully registered openmetrics_hbase for node tl0992hdpe2701.nsw.education\n", "Successfully registered openmetrics_hbase for node tl0992hdpd2701.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0992hdpsq001.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0991hdpsq001.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0992hdpe2701.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0992hdpj2701.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0992hdpj2702.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0992hdpd2701.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0992hdpd2702.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0991hdpe1701.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0991hdpj1701.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0991hdpd1701.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0991hdpd1702.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0991hdpd1704.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0991hdpj1702.nsw.education\n", "Successfully registered openmetrics_hbase for node pl0991hdpd1703.nsw.education\n", "Successfully registered openmetrics_hcmdm for node tl0992hcmdm001.nsw.education\n", "Successfully registered openmetrics_hcmdm for node pl0992hcmdm001.nsw.education\n", "Successfully registered openmetrics_hcrpx for node dl0991hcrpx001.nsw.education\n", "Successfully registered openmetrics_hcrpx for node dl0992hcrpx001.nsw.education\n", "Successfully registered openmetrics_hcrpx for node ql0991hcrpx001.nsw.education\n", "Successfully registered openmetrics_hcrpx for node ql0992hcrpx001.nsw.education\n", "Successfully registered openmetrics_hcrpx for node pl0991hcrpx001.nsw.education\n", "Successfully registered openmetrics_hcrpx for node pl0991hcrpx002.nsw.education\n", "Successfully registered openmetrics_hcrpx for node pl0992hcrpx001.nsw.education\n", "Successfully registered openmetrics_hcrpx for node pl0992hcrpx002.nsw.education\n", "Successfully registered openmetrics_hdpd13 for node pu0992nifigw01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_hdpd13 for node pu0992nifigw02.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_hdpd13 for node pu0991nifigw01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_hdpd13 for node pu0000nifi0001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_hdpd13 for node pu0991nifigw02.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_hpdc for node pw0992hpdc0003.nsw.education\n", "Successfully registered openmetrics_hpdc for node pw0992hpdc0004.nsw.education\n", "Successfully registered openmetrics_hpdc for node pw0992hpdc0002.nsw.education\n", "Successfully registered openmetrics_hpdc for node pw0992hpdc0001.nsw.education\n", "Successfully registered openmetrics_hrwa for node tw0992hrwaap01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_hrwa for node tw0992hrwasr01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_hrwa for node pw0992hrwaap01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_hrwa for node pw0992hrwasr01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_hwme for node pw0992hwmedrm1.nsw.education\n", "Successfully registered openmetrics_hwmg for node pw0991hwmg0101.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_ictp for node ql0475ictp0001.nsw.education\n", "Successfully registered openmetrics_ictp for node tl0475ictp0001.nsw.education\n", "Successfully registered openmetrics_ictp for node pl0475ictp0001.nsw.education\n", "Successfully registered openmetrics_ieauth for node tw0991ieauth01.nsw.education\n", "Successfully registered openmetrics_ieauth for node tw0992ieauth01.nsw.education\n", "Successfully registered openmetrics_ieauth for node tw0991ieauth02.test.nsw.education\n", "Successfully registered openmetrics_ieauth for node tw0992ieauth02.test.nsw.education\n", "Successfully registered openmetrics_ieidd for node dl0991ieidd002.nsw.education\n", "Successfully registered openmetrics_ieidd for node dl0991ieidd001.nsw.education\n", "Successfully registered openmetrics_ieidd for node dl0992ieidd001.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0991ieidd001.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0992ieidd002.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0992ieidd001.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0991ieidd002.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0991ieidd003.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0992ieidd003.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0991ieidd006.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0992ieidd006.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0991ieidd004.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0992ieidd004.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0991ieidd005.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0992ieidd005.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0991ieidd007.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0992ieidd007.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0991ieidd008.nsw.education\n", "Successfully registered openmetrics_ieidd for node pl0992ieidd008.nsw.education\n", "Successfully registered openmetrics_ielog for node ql0991ielog001.nsw.education\n", "Successfully registered openmetrics_ielog for node ql0992ielog001.nsw.education\n", "Successfully registered openmetrics_ielog for node ql0991ielog002.nsw.education\n", "Successfully registered openmetrics_ielog for node pl0991ielog004.nsw.education\n", "Successfully registered openmetrics_ielog for node pl0991ielog003.nsw.education\n", "Successfully registered openmetrics_ielog for node pl0992ielog004.nsw.education\n", "Successfully registered openmetrics_ielog for node pl0992ielog003.nsw.education\n", "Successfully registered openmetrics_ielog for node pl0991ielog005.nsw.education\n", "Successfully registered openmetrics_ielog for node pl0992ielog006.nsw.education\n", "Successfully registered openmetrics_ielog for node pl0991ielog006.nsw.education\n", "Successfully registered openmetrics_ielog for node pl0992ielog005.nsw.education\n", "Successfully registered openmetrics_ifs for node pw0992ifs00001.nsw.education\n", "Successfully registered openmetrics_iirs for node dl0991iirs0001.nsw.education\n", "Successfully registered openmetrics_iirs for node dl0991iirs0002.nsw.education\n", "Successfully registered openmetrics_iirs for node ql0991iirs0001.nsw.education\n", "Successfully registered openmetrics_iirs for node el0992iirs0001.nsw.education\n", "Successfully registered openmetrics_iirs for node ql0992iirs0002.nsw.education\n", "Successfully registered openmetrics_iirs for node ql0991iirs0002.nsw.education\n", "Successfully registered openmetrics_iirs for node tl0991iirs0001.nsw.education\n", "Successfully registered openmetrics_iirs for node tl0991iirs0002.nsw.education\n", "Successfully registered openmetrics_iirs for node pl0991iirs0001.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0992isuscnt1.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0992isuscn01.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0992isuscn02.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0992isuscn03.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0991isuscn03.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0991isuscn04.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0991isuscn05.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0992isuscn04.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0991isuscn10.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0991isuscn09.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0992isuscn08.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0992isuscn07.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0992isuscn06.nsw.education\n", "Successfully registered openmetrics_isuscn for node pw0991isuscn11.nsw.education\n", "Successfully registered openmetrics_isuv for node pw0992isuina01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_itdim for node tw0472itdim001.test.nsw.education\n", "Successfully registered openmetrics_itdim for node pw0472itdim001.nsw.education\n", "Successfully registered openmetrics_jbos for node pl0991jbosmt01.nsw.education\n", "Successfully registered openmetrics_jbos for node pl0992jbosmt02.nsw.education\n", "Successfully registered openmetrics_jbos for node pl0475jbosmt03.nsw.education\n", "Successfully registered openmetrics_jbos17 for node du0000jbos2102.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0992jbweb003.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0000jbos2002.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0992jbweb004.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0000jbos2100.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0000jbos2000.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0000jbos2101.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0000jbos2001.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0991jbweb001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node du0991jbweb002.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0992jbweb004.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0992jbos2100.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0992jbweb003.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0992jbos2101.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0992jbos2000.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0992jbos2002.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0992jbos2001.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0992jbos2102.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0991jbweb001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0991jbos2100.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0991jbos2102.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0991jbweb002.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0991jbos2001.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0991jbos2002.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0991jbos2000.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node qu0991jbos2101.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node tu0000jbos2000.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node tu0000jbos2100.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node tu0000jbos2102.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node tu0000jbos2002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node tu0000jbos2001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node tu0000jbos2101.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0992jbos2100.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0992jbos2102.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0992jbos2000.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0992jbos2002.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0992jbos2101.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0992jbos2001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0992jbweb004.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0992jbweb003.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0991jbweb001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0991jbweb002.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0991jbos2001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0991jbos2102.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0991jbos2000.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0991jbos2101.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0991jbos2002.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jbos17 for node pu0991jbos2100.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_jhsap for node pw0992jhsap001.nsw.education\n", "Successfully registered openmetrics_jhsap for node pw0991jhsap001.nsw.education\n", "Successfully registered openmetrics_jhsm for node qw0992jhsm0001.nsw.education\n", "Successfully registered openmetrics_jhsm for node pw0991jhsm0001.nsw.education\n", "Successfully registered openmetrics_jira for node pl0475jira0002.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkar0001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkab0001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkac0002.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkab0002.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkad0002.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkad0001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkac0001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkaz0001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkar0002.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkac002.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkab002.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkaz001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkac001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkab001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkaz202.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkab201.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkab202.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkaz201.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0475kfkaz203.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkab207.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkab206.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkab205.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkad204.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkad201.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0991kfkad203.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkad202.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kspark0001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kbeam0001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kadm0001.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkac202.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kfkac201.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkaz001.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkaz002.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkab001.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkab003.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkaz003.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkab002.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkac002.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkac001.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfkac003.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0991kfka0001.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0992kfka0001.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0991ksql0001.nsw.education\n", "Successfully registered openmetrics_kfka for node ql0991ksql0002.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0992kadmin02.nsw.education\n", "Successfully registered openmetrics_kfka for node dl0992kadmin02.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkaz001.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkaz003.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkab002.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkab001.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkaz002.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkac002.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkab003.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkac003.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0992kfkac001.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0991ksql0002.nsw.education\n", "Successfully registered openmetrics_kfka for node tl0991ksql0001.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkab0002.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkac0001.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkaz0002.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkaz0001.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkab0001.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkaz0003.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkac0002.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkac0003.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991kfkab0003.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0992kadm0001.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991ksql0001.nsw.education\n", "Successfully registered openmetrics_kfka for node pl0991ksql0002.nsw.education\n", "Successfully registered openmetrics_limg for node dl0991lsoe0008.nsw.education\n", "Successfully registered openmetrics_limg for node dl0991lsoe0015.nsw.education\n", "Successfully registered openmetrics_limg for node pl0992limg0003.nsw.education\n", "Successfully registered openmetrics_limg for node pl0991limg0003.nsw.education\n", "Successfully registered openmetrics_limg for node pl0991limg0001.nsw.education\n", "Successfully registered openmetrics_limg for node pl0992limg0001.nsw.education\n", "Successfully registered openmetrics_limg for node pl0992limg0004.nsw.education\n", "Successfully registered openmetrics_limg for node pl0991limg0004.nsw.education\n", "Successfully registered openmetrics_lsoe for node dw0472lsoe0001.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0001.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0005.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0006.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0002.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0004.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0007.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0014.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0018.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0021.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoekec3.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0024.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991lsoe0020.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0991htest001.nsw.education\n", "Successfully registered openmetrics_lsoe for node dl0992lsoe0015.nsw.education\n", "Successfully registered openmetrics_lsoe for node tl0991cyba0001.nsw.education\n", "Successfully registered openmetrics_lsoe for node tl0991lsoe0001.nsw.education\n", "Successfully registered openmetrics_lsoe for node tl0991lsoe0002.nsw.education\n", "Successfully registered openmetrics_lsoe for node tl0991lsoe0003.nsw.education\n", "Successfully registered openmetrics_lsoe for node tl0991lsoe0004.nsw.education\n", "Successfully registered openmetrics_lsoe for node tl0991lsoe0005.nsw.education\n", "Successfully registered openmetrics_lsoe for node tl0991lsoe0006.nsw.education\n", "Successfully registered openmetrics_lsoe for node pl0991lsoe0001.nsw.education\n", "Successfully registered openmetrics_lsoe for node pl0475lsoe0001.nsw.education\n", "Successfully registered openmetrics_lsoe for node pl0991lsoe0002.nsw.education\n", "Successfully registered openmetrics_lxlc for node pw0000lxlcdm01.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_lxmv for node pw0000lxmve001.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_mantis for node dw0472mantis01.nsw.education\n", "Successfully registered openmetrics_mantis for node dw0472mantis02.nsw.education\n", "Successfully registered openmetrics_mantis for node dw0472mantis06.nsw.education\n", "Successfully registered openmetrics_mantis for node dw0472mantis03.nsw.education\n", "Successfully registered openmetrics_mantis for node dw0472mantis05.nsw.education\n", "Successfully registered openmetrics_mantis for node dw0472mantis04.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0991mantis01.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0991mantis02.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0991mantis03.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0991mantis04.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0472mantis01.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0472mantis02.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0472mantis04.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0472mantis03.nsw.education\n", "Successfully registered openmetrics_mantis for node qw0472mantis01.nsw.education\n", "Successfully registered openmetrics_mantis for node qw0472mantis03.nsw.education\n", "Successfully registered openmetrics_mantis for node qw0472mantis04.nsw.education\n", "Successfully registered openmetrics_mantis for node qw0472mantis02.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0472mantis21.nsw.education\n", "Successfully registered openmetrics_mantis for node qw0472mantis05.nsw.education\n", "Successfully registered openmetrics_mantis for node qw0472mantis06.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0472mantis23.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0472mantis24.nsw.education\n", "Successfully registered openmetrics_mantis for node ew0472mantis22.nsw.education\n", "Successfully registered openmetrics_mantis for node tw0472mantis03.nsw.education\n", "Successfully registered openmetrics_mantis for node tw0472mantis01.nsw.education\n", "Successfully registered openmetrics_mantis for node tw0472mantis05.nsw.education\n", "Successfully registered openmetrics_mantis for node tw0472mantis06.nsw.education\n", "Successfully registered openmetrics_mantis for node tw0472mantis04.nsw.education\n", "Successfully registered openmetrics_mantis for node tw0472mantis02.nsw.education\n", "Successfully registered openmetrics_mantis for node pw0000deiapp05.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_mantis for node pw0472mantis01.nsw.education\n", "Successfully registered openmetrics_mantis for node pw0472mantis02.nsw.education\n", "Successfully registered openmetrics_mantis for node pw0472mantis03.nsw.education\n", "Successfully registered openmetrics_mantis for node pw0472mantis04.nsw.education\n", "Successfully registered openmetrics_mantis for node pw0472mantis05.nsw.education\n", "Successfully registered openmetrics_mantis for node pw0472mantis06.nsw.education\n", "Successfully registered openmetrics_mfaa for node dw0992rds00102.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node qw0991rds00102.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0992mfaw0101.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0992mfaa0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0992mfar0101.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0992mfag0101.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0991mfar0101.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0991mfaw0101.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0991mfaa0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0991mfag0101.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0991web00101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_mfaa for node pw0991mim00701.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_mfas for node pw0992mfas0002.nsw.education\n", "Successfully registered openmetrics_mfas for node pw0992mfas0003.nsw.education\n", "Successfully registered openmetrics_mfas for node pw0992mfas0004.nsw.education\n", "Successfully registered openmetrics_mfile for node pw00000829fs02.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000916fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000910fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw0000archfs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000825fs04.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000825fs03.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000838fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000916fs02.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000490fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000825fs02.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000823fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000829fs04.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000826fs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000829fs03.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mfile for node pw00000910fs02.file.det.nsw.edu.au\n", "Successfully registered openmetrics_mgti for node pw0992mgti0001.prod.det.nsw.edu.au\n", "Successfully registered openmetrics_mgti96 for node pw0000mgti0000.prod.mgmt.det\n", "Successfully registered openmetrics_mige for node cw0992mfaw0102.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node dw0000cesdbi01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node dw0000sapps001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node qw0000sapps001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node qu0991rdew0001.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node qu0991zkps0001.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node qu0991rdew0002.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node pw0000sapps001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node pw0991rwdc0143.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node up-rde-as1.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_mige for node up-rde-as2.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_mim0 for node pw0992mim00701.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_mlms for node pl0991mlms1001.nsw.education\n", "Successfully registered openmetrics_mrdicc for node dw0992mrdicc02.nsw.education\n", "Successfully registered openmetrics_mrdicc for node dw0992mrdicc01.nsw.education\n", "Successfully registered openmetrics_mrdicc for node qw0992mrdicc02.nsw.education\n", "Successfully registered openmetrics_mrdicc for node qw0992mrdicc01.nsw.education\n", "Successfully registered openmetrics_mrdicc for node tw0992mrdicc01.nsw.education\n", "Successfully registered openmetrics_mrdicc for node tw0992mrdicc02.nsw.education\n", "Successfully registered openmetrics_mrdicc for node pw0992mrdicc01.nsw.education\n", "Successfully registered openmetrics_mrdicc for node pw0992mrdicc02.nsw.education\n", "Successfully registered openmetrics_mseg for node dl0991mseg0001.nsw.education\n", "Successfully registered openmetrics_mseg for node dl0991mseg0002.nsw.education\n", "Successfully registered openmetrics_mseg for node dl0992mseg0001.nsw.education\n", "Successfully registered openmetrics_mseg for node dl0992mseg0002.nsw.education\n", "Successfully registered openmetrics_mseg for node ql0991mseg0001.nsw.education\n", "Successfully registered openmetrics_mseg for node ql0991mseg0002.nsw.education\n", "Successfully registered openmetrics_mseg for node ql0992mseg0002.nsw.education\n", "Successfully registered openmetrics_mseg for node ql0992mseg0001.nsw.education\n", "Successfully registered openmetrics_mseg for node pl0000mseg0004.nsw.education\n", "Successfully registered openmetrics_mseg for node pl0000mseg0003.nsw.education\n", "Successfully registered openmetrics_mseg for node pl0000mseg0001.nsw.education\n", "Successfully registered openmetrics_mseg for node pl0000mseg0002.nsw.education\n", "Successfully registered openmetrics_msgb for node du0992msgbus01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node qu0992apigat01.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node qu0992msgbus10.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node qu0992apigat10.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node qu0000csndra01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node qu0991apigat01.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node qu0991msgbus01.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node tu0992apigat01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node tu0991amxgrd01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node tu0991msgbus01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node tu0991apigat01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node pu0992apigat01.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node pu0992msgbus01.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node pu0992csndra01.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node pu0991apigat01.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_msgb for node pu0991msgbus01.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_msgbus for node dl0475msgbus01.nsw.education\n", "Successfully registered openmetrics_msgbus for node tl0475msgbus01.nsw.education\n", "Successfully registered openmetrics_msgbus for node tl0475msgbus02.nsw.education\n", "Successfully registered openmetrics_mskms for node pw0991mskms001.nsw.education\n", "Successfully registered openmetrics_mskms for node pw0992mskms001.nsw.education\n", "Successfully registered openmetrics_mtmrep for node pl0991mtmrep01.nsw.education\n", "Successfully registered openmetrics_myplbi for node pw0472myplbi01.nsw.education\n", "Successfully registered openmetrics_nbck for node tw0992nbckms01.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node tw0992nbckop01.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node tw0992nbckmv01.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node tw0991nbckcb01.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node tw0992nbckcb01.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node hpvstmas0012.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node pw0000bkms0001.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node pw0991nbckms10.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node pw0991nbckop10.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node pw0991nbckmv11.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node pw0991nbckmv12.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node pw0992nbckmv11.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node pw0992nbckmv12.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbck for node tw0991nbckmv01.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_nbcklx for node tl0992nbckmv07.backup.det.nsw.edu.au\n", "Successfully registered openmetrics_ncfb for node du0991ncfb0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_ncfb for node pu0992ncfb0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_ncfb for node pu0991ncfb0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_ndes for node tw0992ndes0001.nsw.education\n", "Successfully registered openmetrics_ndes for node tw0992ndes0102.test.nsw.education\n", "Successfully registered openmetrics_ndes for node pw0992ndes0001.nsw.education\n", "Successfully registered openmetrics_ndes for node pw0991ndes0001.nsw.education\n", "Successfully registered openmetrics_ndes for node pw0992ndes0102.nsw.education\n", "Successfully registered openmetrics_ndes for node pw0991ndes0102.nsw.education\n", "Successfully registered openmetrics_nexus for node pl0991nexus001.nsw.education\n", "Successfully registered openmetrics_nexus for node tl0475nexus001.nsw.education\n", "Successfully registered openmetrics_nexus for node tl0992nexus001.nsw.education\n", "Successfully registered openmetrics_nexus for node pl0475nexus001.nsw.education\n", "Successfully registered openmetrics_nexus for node pl0991nexuslb1.nsw.education\n", "Successfully registered openmetrics_nexus for node pl0991nexuslb2.nsw.education\n", "Successfully registered openmetrics_ngcm for node dl0992ngcm0002.nsw.education\n", "Successfully registered openmetrics_ngcm for node pl0991ngcm0001.nsw.education\n", "Successfully registered openmetrics_ngcm for node pl0992ngcm0001.nsw.education\n", "Successfully registered openmetrics_ngcm for node dl0991ngcm0001.nsw.education\n", "Successfully registered openmetrics_ngsfpa for node qw0472ngsfpa01.nsw.education\n", "Successfully registered openmetrics_ngsfpa for node pw0472ngsfpa01.nsw.education\n", "Successfully registered openmetrics_npac for node tl0992npac0001.nsw.education\n", "Successfully registered openmetrics_npac for node pl0991npac0001.nsw.education\n", "Successfully registered openmetrics_npac for node pl0992npac0001.nsw.education\n", "Successfully registered openmetrics_npac for node pl0991npac0002.nsw.education\n", "Successfully registered openmetrics_npac for node pl0992npac0002.nsw.education\n", "Successfully registered openmetrics_nps0 for node pw0992nps01101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_nps0 for node pw0991nps01101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_nps0 for node pw0992nps00101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_nps0 for node pw0992nps00102.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_nps0 for node pw0991nps00101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_nwgf for node dl0991nwgf0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwgf for node pl0991nwgf0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwix for node pw0991nwixia01.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwki for node qu0992nwkit001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwki5a for node pu0991nwkit001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwtst for node qw0992nwtst001.nsw.education\n", "Successfully registered openmetrics_nwtst for node ql0992nwtst001.nsw.education\n", "Successfully registered openmetrics_nwtst for node tw0992nwtst001.nsw.education\n", "Successfully registered openmetrics_nwtst for node tl0992nwtst001.nsw.education\n", "Successfully registered openmetrics_nwut for node tw0991nwut0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwut for node pw0992nwut0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwut for node pw0991nwut0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node dl0991nwzb0051.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node dl0991nwpg0051.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node dl0991nwzb0052.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node dl0991nwzb0053.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0991nwpg0002.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0992nwpg0002.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0991nwzb0601.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0991nwzb0602.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0991nwzb0603.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0991nwzb0604.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0992nwzb0601.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0992nwzb0602.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb for node pl0992nwzb0603.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb30 for node pu0991nicm0002.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_nwzb30 for node pu0991nsql001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_obs for node pl0475obsrpx01.nsw.education\n", "Successfully registered openmetrics_obs for node dw0991obs00001.nsw.education\n", "Successfully registered openmetrics_obs for node dl0992obsmrx01.nsw.education\n", "Successfully registered openmetrics_obs for node dl0992obsken01.nsw.education\n", "Successfully registered openmetrics_obs for node tl0992obscol01.nsw.education\n", "Successfully registered openmetrics_obs for node tl0992obscol03.nsw.education\n", "Successfully registered openmetrics_obs for node tl0992obscol02.nsw.education\n", "Successfully registered openmetrics_obs for node pl0992obscol01.nsw.education\n", "Successfully registered openmetrics_obs for node pl0992obscol03.nsw.education\n", "Successfully registered openmetrics_obs for node pl0992obscol02.nsw.education\n", "Successfully registered openmetrics_obs for node tl0992obsobs01.test.nsw.education\n", "Successfully registered openmetrics_obs for node tl0992obs00001.test.nsw.education\n", "Successfully registered openmetrics_obs for node tl0992obscol05.nsw.education\n", "Successfully registered openmetrics_obs for node tl0992obscol04.nsw.education\n", "Successfully registered openmetrics_obs for node tl0992obscol06.nsw.education\n", "Successfully registered openmetrics_obs for node tl0991obsbld01.nsw.education\n", "Successfully registered openmetrics_obs for node pw0991obs00001.nsw.education\n", "Successfully registered openmetrics_obs for node pl0475dbphd002.nsw.education\n", "Successfully registered openmetrics_obs for node pl0991obskb01.nsw.education\n", "Successfully registered openmetrics_obs for node pl0992obsobs01.nsw.education\n", "Successfully registered openmetrics_obs for node pl0475obscol06.nsw.education\n", "Successfully registered openmetrics_obs for node pl0475obscol08.nsw.education\n", "Successfully registered openmetrics_obs for node pl0475obscol07.nsw.education\n", "Successfully registered openmetrics_obs for node pl0475obsrh9.nsw.education\n", "Successfully registered openmetrics_olvr for node tw0472slap0001.test.nsw.education\n", "Successfully registered openmetrics_olvr for node tw0472slrp0001.test.nsw.education\n", "Successfully registered openmetrics_olvr for node tw0472slrt0001.test.nsw.education\n", "Successfully registered openmetrics_olvr for node tw0472slrt0002.test.nsw.education\n", "Successfully registered openmetrics_omda for node qw0000omdacg01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node qw0000omdacg51.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node tw0000omdacg01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node tw0000omdacg51.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node pw0992omdacm01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node pw0991omdacm01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node pw0991omdacm03.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node pw0991omdacm02.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node dw0000omdacg01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node pw0000omdacg02.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node pw0000omdacg01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omda for node dw0000omdacg52.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omdc for node qw0000scommg02.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omdc for node qw0000scommg01.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omdc for node tw0000momgw002.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omdc for node pw0992momgw502.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omdc for node dw0000momgw002.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omdc for node pw0991momgw701.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omdc for node pw0991momgw501.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_omdc for node pw0991momgw502.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_orae2m for node dl0475orae2m01.nsw.education\n", "Successfully registered openmetrics_orae2m for node dl0475orae2m02.nsw.education\n", "Successfully registered openmetrics_oram for node ql0991oram0001.nsw.education\n", "Successfully registered openmetrics_oram for node ql0991oram0002.nsw.education\n", "Successfully registered openmetrics_oram for node pl0991oramd101.nsw.education\n", "Successfully registered openmetrics_oram for node pl0992oramd201.nsw.education\n", "Successfully registered openmetrics_oram for node pl0991orama101.nsw.education\n", "Successfully registered openmetrics_oram for node pl0991orama102.nsw.education\n", "Successfully registered openmetrics_oram for node pl0992orama201.nsw.education\n", "Successfully registered openmetrics_oram for node pl0991orama103.nsw.education\n", "Successfully registered openmetrics_oram for node pl0992orama203.nsw.education\n", "Successfully registered openmetrics_oram for node pl0992orama202.nsw.education\n", "Successfully registered openmetrics_oram for node dl0991orama101.nsw.education\n", "Successfully registered openmetrics_oram for node dl0991orama102.nsw.education\n", "Successfully registered openmetrics_otd for node pw0472otd00001.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0992panuid01.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0991panuid01.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0991panuid03.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0991panuid04.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0991panuid05.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0991panuid02.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0992panuid02.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0992panuid04.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0992panuid03.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0992panuid05.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0991panuidc1.nsw.education\n", "Successfully registered openmetrics_panuid for node pw0992panuidc1.nsw.education\n", "Successfully registered openmetrics_pgdb for node dl0991pgdb0001.nsw.education\n", "Successfully registered openmetrics_pgdb for node dl0991pgdbbrmd.nsw.education\n", "Successfully registered openmetrics_pgdb for node ql0992pgdb0001.nsw.education\n", "Successfully registered openmetrics_pgdb for node tl0992pgdb0001.nsw.education\n", "Successfully registered openmetrics_pgdb for node tl0991pgdb0002.nsw.education\n", "Successfully registered openmetrics_pgdb for node tl0992pgdb0002.nsw.education\n", "Successfully registered openmetrics_pgdb for node tl0991pgdb0003.nsw.education\n", "Successfully registered openmetrics_pgdb for node tl0991pgdb0004.nsw.education\n", "Successfully registered openmetrics_pgdb for node tl0991pgdb0005.nsw.education\n", "Successfully registered openmetrics_pgdb for node pl0992pgdb0001.nsw.education\n", "Successfully registered openmetrics_pnxz for node du0992pnxzvr01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_pnxz for node du0991pnxzvr01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_pnxz34 for node qu0991pnxzvr01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_pnxz34 for node qu0992pnxzvr01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_pnxz34 for node pu0992pnxzvr01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_pnxz34 for node pu0992pnxzvr02.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_pnxz34 for node pu0991pnxzvr02.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_pnxz34 for node pu0991pnxzvr01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_ppjh for node pw0991ppjh0101.nsw.education\n", "Successfully registered openmetrics_psql for node du0992psql0002.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_psql for node du0992psql0001.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_psql33 for node tu0000psqlp003.dbs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_psql33 for node tu0000psqlp002.dbs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_psql33 for node tu0000psqlp001.dbs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_qaauto for node tw0992qaauto01.nsw.education\n", "Successfully registered openmetrics_qaauto for node tw0991qaauto01.nsw.education\n", "Successfully registered openmetrics_qaauto for node tw0991qaauto02.nsw.education\n", "Successfully registered openmetrics_qaauto for node pw0992qaauto01.nsw.education\n", "Successfully registered openmetrics_qaauto for node pw0992qaauto02.nsw.education\n", "Successfully registered openmetrics_qaauto for node pw0991qapoc01.nsw.education\n", "Successfully registered openmetrics_qaauto for node pw0991qaauto01.nsw.education\n", "Successfully registered openmetrics_qaauto for node pw0991qaauto02.nsw.education\n", "Successfully registered openmetrics_qaauto for node pw0991qaauto03.nsw.education\n", "Successfully registered openmetrics_qaauto for node pw0991qaauto04.nsw.education\n", "Successfully registered openmetrics_qaauto for node pw0472qaauto01.nsw.education\n", "Successfully registered openmetrics_qahq for node tw0000qahqc001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_qahq for node pw0000qahqc001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_qalr for node qw0992qalrg101.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg102.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg105.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrc101.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalr0001.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg101.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg102.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg103.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg104.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg103.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg104.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg105.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg106.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg107.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg107.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg108.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg109.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg110.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg109.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg110.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg111.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg112.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg113.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg114.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg115.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg116.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg117.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg120.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg121.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg123.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg124.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg125.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg126.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg127.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg128.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg129.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg132.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg133.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg134.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg135.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg137.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg138.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg140.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg118.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg119.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg122.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg130.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg136.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg139.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg131.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg111.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg113.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg114.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg115.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg116.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg117.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg118.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg119.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg121.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg122.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg123.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg124.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg125.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg126.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg127.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg129.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg133.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg134.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg135.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg139.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg140.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg112.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg120.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg130.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg131.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg132.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg136.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg137.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg128.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg138.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg108.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg106.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg501.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg501.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0992qalrg502.nsw.education\n", "Successfully registered openmetrics_qalr for node qw0991qalrg502.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi03.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoc01.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi02.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi04.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi01.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi05.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi09.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi08.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi11.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi06.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi10.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi12.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi13.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi14.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi15.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi17.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi19.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi16.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi18.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi20.nsw.education\n", "Successfully registered openmetrics_qaneo for node qw0992qaneoi07.nsw.education\n", "Successfully registered openmetrics_rdeapp for node qw0472rdeapp01.nsw.education\n", "Successfully registered openmetrics_rdeapp for node pw0472rdeapp01.nsw.education\n", "Successfully registered openmetrics_rdgs for node cw0991rdgs0102.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_rds0 for node dw0992rds00101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_rds047 for node dw0991rds00501.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_rds047 for node tw0991rds00501.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_rds047 for node pw0991rds00501.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_rds057 for node qw0991rds00501.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_rds077 for node qw0991rds00101.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_react for node pw0991react003.nsw.education\n", "Successfully registered openmetrics_remdus for node pw0000mypldm01.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_remw for node pu0000remw1001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_remw for node pu0000rema1003.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_remw for node pu0000rema1001.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_remw for node pu0000remw1004.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_remw for node pu0000remw1003.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_remw for node pu0000rema1002.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_remw for node pu0000remw1002.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_resolv for node qw0992resolv01.nsw.education\n", "Successfully registered openmetrics_resolv for node qw0992resolv02.pre.nsw.education\n", "Successfully registered openmetrics_resolv for node tw0992resolv01.nsw.education\n", "Successfully registered openmetrics_resolv for node pw0991resolv01.nsw.education\n", "Successfully registered openmetrics_resolv for node pw0991resolv02.nsw.education\n", "Successfully registered openmetrics_rest for node pl0991rest0001.nsw.education\n", "Successfully registered openmetrics_rest for node pl0992rest0001.nsw.education\n", "Successfully registered openmetrics_rredp for node pw0992rredp001.nsw.education\n", "Successfully registered openmetrics_rredp for node pw0992rredp002.nsw.education\n", "Successfully registered openmetrics_rredp for node pw0992rredp004.nsw.education\n", "Successfully registered openmetrics_rredp for node pw0992rredp005.nsw.education\n", "Successfully registered openmetrics_rredp for node pw0992rredp003.nsw.education\n", "Successfully registered openmetrics_rredp for node pw0992rredp006.nsw.education\n", "Successfully registered openmetrics_rsfm for node pw0992rsfm0001.nsw.education\n", "Successfully registered openmetrics_rwdc for node dw0992rwdc0006.nsw.education\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0139.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0132.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0133.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0109.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0134.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0125.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0130.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0143.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0129.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0121.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0135.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0140.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0142.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0136.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0123.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0137.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0114.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0120.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0122.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0141.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0138.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0113.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0121.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0109.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0120.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0136.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0137.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0129.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0130.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0141.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0139.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0114.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0138.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0123.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0133.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0134.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0132.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0135.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0113.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0140.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0122.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0142.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0125.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0302.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0301.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0302.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0301.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0203.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0204.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0202.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0203.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0202.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0204.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc1301.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0503.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0992rwdc0702.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0603.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0702.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0503.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_rwdc12 for node pw0991rwdc0504.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_saclus for node dl0991saclus03.nsw.education\n", "Successfully registered openmetrics_saclus for node dl0991saclus01.nsw.education\n", "Successfully registered openmetrics_saclus for node dl0991saclus02.nsw.education\n", "Successfully registered openmetrics_salm for node qw0472salmbs01.nsw.education\n", "Successfully registered openmetrics_salm for node qw0472salmbsz1.nsw.education\n", "Successfully registered openmetrics_salm for node ew0472salmbs01.nsw.education\n", "Successfully registered openmetrics_salm for node pw0472salm0001.nsw.education\n", "Successfully registered openmetrics_salm for node tw0472salm0001.nsw.education\n", "Successfully registered openmetrics_salm for node tw0472salm0002.nsw.education\n", "Successfully registered openmetrics_salm for node tw0472salmbs01.test.nsw.education\n", "Successfully registered openmetrics_salm for node tw0472salmbsa1.test.nsw.education\n", "Successfully registered openmetrics_salm for node pw0472salm0002.nsw.education\n", "Successfully registered openmetrics_salm for node qw0472salm0001.nsw.education\n", "Successfully registered openmetrics_salm for node ew0472salm0001.nsw.education\n", "Successfully registered openmetrics_salm for node qw0472salm0002.nsw.education\n", "Successfully registered openmetrics_salm for node pw0472salmbs01.nsw.education\n", "Successfully registered openmetrics_sap114 for node tw0992sap114h1.nsw.education\n", "Successfully registered openmetrics_sapa for node pw0991sapaps1.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sapa for node pw0991sapdba1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sapcc for node dl0992sapcc202.nsw.education\n", "Successfully registered openmetrics_sapcc for node dl0992sapcc201.nsw.education\n", "Successfully registered openmetrics_sapcc for node dl0991sapcc102.nsw.education\n", "Successfully registered openmetrics_sapcc for node dl0991sapcc101.nsw.education\n", "Successfully registered openmetrics_sapcc for node ql0991sapcc101.nsw.education\n", "Successfully registered openmetrics_sapcc for node ql0991sapcc102.nsw.education\n", "Successfully registered openmetrics_sapcc for node ql0992sapcc201.nsw.education\n", "Successfully registered openmetrics_sapcc for node ql0992sapcc202.nsw.education\n", "Successfully registered openmetrics_sapcc for node pl0992sapcc202.nsw.education\n", "Successfully registered openmetrics_sapcc for node pl0992sapcc201.nsw.education\n", "Successfully registered openmetrics_sapcc for node pl0991sapcc101.nsw.education\n", "Successfully registered openmetrics_sapcc for node pl0991sapcc102.nsw.education\n", "Successfully registered openmetrics_sapd for node cw0992smssa01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node cw0992sapdb01.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node cw0992sapdb03.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node cw0992sasua01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node cw0992sapdb02.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node cw0992smsba01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node dw0991smdsa01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node pw0992sapdba1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node pw0992sapapu1.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd for node pw0992sapdbt1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd52 for node pw0991sapdbt1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd54 for node cw0991sapdb01.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd54 for node cw0991sapdb02.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd54 for node dw0991sapdb01.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapd54 for node dw0991sapdb02.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sapia for node qw0472sapia001.pre.nsw.education\n", "Successfully registered openmetrics_sapia for node qw0472sapia002.pre.nsw.education\n", "Successfully registered openmetrics_sapia for node qw0472sapia003.pre.nsw.education\n", "Successfully registered openmetrics_sapia for node tw0472sapia001.test.nsw.education\n", "Successfully registered openmetrics_sapia for node tw0472sapia002.test.nsw.education\n", "Successfully registered openmetrics_sapia for node tw0472sqs006n1.test.nsw.education\n", "Successfully registered openmetrics_sapia for node pw0472sapia006.nsw.education\n", "Successfully registered openmetrics_sapia for node pw0472sapia002.nsw.education\n", "Successfully registered openmetrics_sapia for node pw0472sapia005.nsw.education\n", "Successfully registered openmetrics_sapia for node pw0472sapia004.nsw.education\n", "Successfully registered openmetrics_sapia for node pw0472sapia001.nsw.education\n", "Successfully registered openmetrics_sapia for node pw0472sapia003.nsw.education\n", "Successfully registered openmetrics_sapia for node pw0472sapia007.nsw.education\n", "Successfully registered openmetrics_sapiq for node dl0992sapiqd01.nsw.education\n", "Successfully registered openmetrics_sapiq for node dl0991sapiqd01.nsw.education\n", "Successfully registered openmetrics_sapiq for node dl0992sapiqd02.nsw.education\n", "Successfully registered openmetrics_saprt for node dl0992srti01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_saprt for node dl0991srti01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_saprt for node ql0991srte01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_saprt for node ql0991srti01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_saprt for node tl0991srte01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_saprt for node pl0992srte01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_saprt for node pl0992srti01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_saprt for node pl0991srte01.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_saprt for node pl0991srti01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sapwd for node dl0991sapwdc01.nsw.education\n", "Successfully registered openmetrics_sapwd for node dl0991sapwdc02.nsw.education\n", "Successfully registered openmetrics_sapwd for node dl0992sapwdc01.nsw.education\n", "Successfully registered openmetrics_sapwd for node dl0992sapwdc02.nsw.education\n", "Successfully registered openmetrics_sas114 for node qw0992sas114h1.nsw.education\n", "Successfully registered openmetrics_sas114 for node pw0991sas114h1.nsw.education\n", "Successfully registered openmetrics_sas115 for node qw0992sas115h1.nsw.education\n", "Successfully registered openmetrics_sas115 for node pw0991sas115h1.nsw.education\n", "Successfully registered openmetrics_satc for node pl0992satc0001.nsw.education\n", "Successfully registered openmetrics_satc for node pl0991satc0001.nsw.education\n", "Successfully registered openmetrics_satc for node pl0992satc0005.nsw.education\n", "Successfully registered openmetrics_sats for node pu0991sats0001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_sc0 for node cl0992sc0la00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc0 for node cl0992sc0ga00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc0 for node cl0992sc0cs01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc0 for node cl0992sc0fa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc0 for node cl0992sc0cs03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc0 for node cl0992sc0na00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb13.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb14.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb15.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb07.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9fa01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9oa01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9oa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ka01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9fa03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na10.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na06.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na12.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na09.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9va01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na05.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9fa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na07.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na08.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb08.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb06.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9pa05.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9fa02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ga03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ka00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb09.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9va05.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9va00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9cs04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9cs03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ua00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9va07.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9va04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9cs01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9pa02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9pa03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9wd02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9pa01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9va03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb05.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9wd01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9va02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9pa04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9pa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9wd03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ua01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb10.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb12.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ga02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ta01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9fa04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ja00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9wd04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9va06.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9fa06.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9fa05.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na11.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9fa07.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ta00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9cs02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ja01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ga00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9ga01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9nb11.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sc9 for node cl0991sc9na13.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_scne for node pw0991scnet001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_scoma for node dw0992scomag01.dev.nsw.education\n", "Successfully registered openmetrics_scomd for node pw0991scomdm01.nsw.education\n", "Successfully registered openmetrics_scomd for node pw0992scomdm01.nsw.education\n", "Successfully registered openmetrics_scomd for node pw0992scomdm02.nsw.education\n", "Successfully registered openmetrics_scomd for node pw0991scomdm02.nsw.education\n", "Successfully registered openmetrics_scomd for node dw0992scomdg01.dev.nsw.education\n", "Successfully registered openmetrics_scomd for node qw0992scomdg01.pre.nsw.education\n", "Successfully registered openmetrics_scomd for node tw0992scomdg01.test.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem02.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem07.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem03.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem04.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem05.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem06.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem08.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem09.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem10.nsw.education\n", "Successfully registered openmetrics_scome for node pw0991scomem01.nsw.education\n", "Successfully registered openmetrics_scomq for node qw0991scomqm01.pre.nsw.education\n", "Successfully registered openmetrics_scomq for node qw0992scomqm01.pre.nsw.education\n", "Successfully registered openmetrics_scomq for node dw0992scomqg01.dev.nsw.education\n", "Successfully registered openmetrics_scomq for node qw0992scomqg01.pre.nsw.education\n", "Successfully registered openmetrics_scomq for node tw0992scomqg01.test.nsw.education\n", "Successfully registered openmetrics_scord for node qw0992scordms1.pre.nsw.education\n", "Successfully registered openmetrics_scord for node qw0992scordws1.pre.nsw.education\n", "Successfully registered openmetrics_scord for node qw0992scordrd1.pre.nsw.education\n", "Successfully registered openmetrics_scord for node pw0992scordrs1.nsw.education\n", "Successfully registered openmetrics_scord for node pw0992scordrd1.nsw.education\n", "Successfully registered openmetrics_scord for node pw0991scordrs1.nsw.education\n", "Successfully registered openmetrics_scord for node pw0991scordrs2.nsw.education\n", "Successfully registered openmetrics_scord for node pw0992scordrs2.nsw.education\n", "Successfully registered openmetrics_scord for node qw0992scordrs1.pre.nsw.education\n", "Successfully registered openmetrics_scord for node qw0991scordrs1.pre.nsw.education\n", "Successfully registered openmetrics_scord for node pw0992scordms1.nsw.education\n", "Successfully registered openmetrics_scord for node pw0991scordws1.nsw.education\n", "Successfully registered openmetrics_scord for node pw0992scordws1.nsw.education\n", "Successfully registered openmetrics_score for node qw0992scorems1.pre.nsw.education\n", "Successfully registered openmetrics_score for node qw0992scorers1.pre.nsw.education\n", "Successfully registered openmetrics_score for node qw0991scorers1.pre.nsw.education\n", "Successfully registered openmetrics_score for node qw0992scorews1.pre.nsw.education\n", "Successfully registered openmetrics_score for node qw0992scorerd1.pre.nsw.education\n", "Successfully registered openmetrics_score for node pw0992scorers1.nsw.education\n", "Successfully registered openmetrics_score for node pw0991scorers1.nsw.education\n", "Successfully registered openmetrics_score for node pw0992scorews1.nsw.education\n", "Successfully registered openmetrics_score for node pw0992scorerd1.nsw.education\n", "Successfully registered openmetrics_score for node pw0992scorems1.nsw.education\n", "Successfully registered openmetrics_sd0 for node dl0992sd0nb00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0cs04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0ja00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0nb02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0wd01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0ua00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0na01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0oa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0wd04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0fa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0ta00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0ka00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0na00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0wd03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0cs02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0va00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0pa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0ga00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0nb01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0cs03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0wd02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0nb03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd0 for node dl0992sd0cs01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1fa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1ta00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1va00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1wd02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1cs03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1cs01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1nb00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1cs02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1wd04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1oa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1nb03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1wd01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1ja00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1cs04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1pa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1ga00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1nb01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1na01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1ka00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1na00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1ua00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1wd03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd1 for node dl0991sd1nb02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7na00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7ga00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7wd01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7fa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7nb00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7na01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7va00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7wd02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7ua00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7cs01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7pa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7oa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7cs02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7cs03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd7 for node dl0991sd7ta00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8na00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8pa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8va00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8ga00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8ua00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8wd01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8wd02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8wd03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8wd04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8cs01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8cs02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8cs03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8cs04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8fa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8ta00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8oa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8ja00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8na01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8nb00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8nb01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8nb02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sd8 for node dl0992sd8nb03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sdetst for node dw0992sdetst02.nsw.education\n", "Successfully registered openmetrics_sdetst for node dw0992sdetst01.dev.nsw.education\n", "Successfully registered openmetrics_sdetst for node dw0472sdetst01.dev.nsw.education\n", "Successfully registered openmetrics_sdetst for node qw0992sdetst01.pre.nsw.education\n", "Successfully registered openmetrics_sdetst for node tw0472sdetst01.nsw.education\n", "Successfully registered openmetrics_sdetst for node tw0992sdetst01.nsw.education\n", "Successfully registered openmetrics_sdetst for node tw0475sdetst01.nsw.education\n", "Successfully registered openmetrics_sdetst for node tw0992sdetst02.test.nsw.education\n", "Successfully registered openmetrics_sdetst for node tw0472sdetst02.test.nsw.education\n", "Successfully registered openmetrics_sdetst for node pw0472sdetst01.nsw.education\n", "Successfully registered openmetrics_sdetst for node pw0991sdetst01.nsw.education\n", "Successfully registered openmetrics_se1 for node el0992se1na01.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1cs03.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1wd01.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1wd02.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1oa00.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1na00.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1fa00.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1pa00.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1ga01.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1cs04.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1ta00.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1wd03.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1cs02.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1va00.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1wd04.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1ga00.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_se1 for node el0992se1cs01.apps.train.det.nsw.edu.au\n", "Successfully registered openmetrics_sedrms for node cw0992sedrms01.nsw.education\n", "Successfully registered openmetrics_sedrms for node cw0992sedrms02.nsw.education\n", "Successfully registered openmetrics_sedrms for node dw0992sedrms01.nsw.education\n", "Successfully registered openmetrics_sedrms for node dw0992sedrms02.nsw.education\n", "Successfully registered openmetrics_sedrms for node qw0991sedrms01.nsw.education\n", "Successfully registered openmetrics_sedrms for node qw0991sedrms02.nsw.education\n", "Successfully registered openmetrics_sedrms for node tw0991sedrms01.nsw.education\n", "Successfully registered openmetrics_sedrms for node tw0991sedrms02.nsw.education\n", "Successfully registered openmetrics_sedrms for node pw0991sedrms01.nsw.education\n", "Successfully registered openmetrics_sedrms for node pw0991sedrms02.nsw.education\n", "Successfully registered openmetrics_seismo for node pl0991seismo01.nsw.education\n", "Successfully registered openmetrics_sems for node dl0991sems0001.nsw.education\n", "Successfully registered openmetrics_sems for node dl0991sems0002.nsw.education\n", "Successfully registered openmetrics_serg for node pw0472serg0001.nsw.education\n", "Successfully registered openmetrics_sfile for node pw0992sfile001.nsw.education\n", "Successfully registered openmetrics_sibi for node tw0992sibii001.nsw.education\n", "Successfully registered openmetrics_sibi for node tw0992sibia001.nsw.education\n", "Successfully registered openmetrics_sibi for node pw0992sibii001.nsw.education\n", "Successfully registered openmetrics_sibi for node pw0991sibii001.nsw.education\n", "Successfully registered openmetrics_sibi for node pw0992sibia001.nsw.education\n", "Successfully registered openmetrics_sibi for node pw0991sibia001.nsw.education\n", "Successfully registered openmetrics_sima for node dw0992sima0001.nsw.education\n", "Successfully registered openmetrics_sima for node dw0992sima0002.nsw.education\n", "Successfully registered openmetrics_sima for node qw0992sima0002.nsw.education\n", "Successfully registered openmetrics_sima for node qw0992sima0001.nsw.education\n", "Successfully registered openmetrics_sima for node qw0992sima0004.nsw.education\n", "Successfully registered openmetrics_sima for node qw0992sima0003.nsw.education\n", "Successfully registered openmetrics_sima for node qw0992sima0006.nsw.education\n", "Successfully registered openmetrics_sima for node qw0992sima0005.nsw.education\n", "Successfully registered openmetrics_sima for node qw0992sima0011.nsw.education\n", "Successfully registered openmetrics_sima for node qw0992sima0012.nsw.education\n", "Successfully registered openmetrics_sima for node pw0991sima0007.nsw.education\n", "Successfully registered openmetrics_sima for node tw0992sima0002.nsw.education\n", "Successfully registered openmetrics_sima for node tw0992sima0001.nsw.education\n", "Successfully registered openmetrics_sima for node pw0991sima0004.nsw.education\n", "Successfully registered openmetrics_sima for node pw0991sima0003.nsw.education\n", "Successfully registered openmetrics_sima for node pw0991sima0001.nsw.education\n", "Successfully registered openmetrics_sima for node pw0991sima0002.nsw.education\n", "Successfully registered openmetrics_sima for node pw0991sima0005.nsw.education\n", "Successfully registered openmetrics_sima for node pw0991sima0006.nsw.education\n", "Successfully registered openmetrics_siobi for node qw0992siobi001.nsw.education\n", "Successfully registered openmetrics_siobi for node tw0992siobi001.nsw.education\n", "Successfully registered openmetrics_siobi for node pw0992siobi001.nsw.education\n", "Successfully registered openmetrics_siop for node qw0991siop0004.nsw.education\n", "Successfully registered openmetrics_siop for node qw0991siop0002.nsw.education\n", "Successfully registered openmetrics_siop for node qw0992siop0002.nsw.education\n", "Successfully registered openmetrics_siop for node qw0992siop0001.nsw.education\n", "Successfully registered openmetrics_siop for node qw0991siop0001.nsw.education\n", "Successfully registered openmetrics_siop for node qw0992siop0003.nsw.education\n", "Successfully registered openmetrics_siop for node qw0991siop0003.nsw.education\n", "Successfully registered openmetrics_siop for node qw0992siop0004.nsw.education\n", "Successfully registered openmetrics_siop for node pw0992siop0005.nsw.education\n", "Successfully registered openmetrics_siop for node tw0991siop0004.nsw.education\n", "Successfully registered openmetrics_siop for node tw0991siop0002.nsw.education\n", "Successfully registered openmetrics_siop for node tw0991siop0003.nsw.education\n", "Successfully registered openmetrics_siop for node tw0991siop0001.nsw.education\n", "Successfully registered openmetrics_siop for node pw0991siop0003.nsw.education\n", "Successfully registered openmetrics_siop for node pw0991siop0004.nsw.education\n", "Successfully registered openmetrics_siop for node pw0991siop0002.nsw.education\n", "Successfully registered openmetrics_siop for node pw0992siop0001.nsw.education\n", "Successfully registered openmetrics_siop for node pw0992siop0004.nsw.education\n", "Successfully registered openmetrics_siop for node pw0992siop0003.nsw.education\n", "Successfully registered openmetrics_siop for node pw0991siop0001.nsw.education\n", "Successfully registered openmetrics_siop for node pw0992siop0002.nsw.education\n", "Successfully registered openmetrics_sip114 for node tw0992sip114h1.nsw.education\n", "Successfully registered openmetrics_sis115 for node qw0992sis115h1.nsw.education\n", "Successfully registered openmetrics_sis115 for node pw0991sis115h1.nsw.education\n", "Successfully registered openmetrics_slam for node pw0992slam0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_smaw for node pw0991smaw0001.nsw.education\n", "Successfully registered openmetrics_smbps for node pw0992smbps001.nsw.education\n", "Successfully registered openmetrics_smbps for node pw0991smbps001.nsw.education\n", "Successfully registered openmetrics_smbps for node pw0992smbps002.nsw.education\n", "Successfully registered openmetrics_smcms for node pw0991smcms001.nsw.education\n", "Successfully registered openmetrics_smft1a for node tl0991smft9001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_smft1a for node tl0992smft9001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_smft1a for node pl0991smft9001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_smft1a for node pl0992smft9001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_smft1a for node pu0991smft0001.dmz.det.nsw.edu.au\n", "Successfully registered openmetrics_smqa for node qw0991smqas001.nsw.education\n", "Successfully registered openmetrics_smqa for node qw0992smqas002.nsw.education\n", "Successfully registered openmetrics_smqa for node qw0992smqas001.nsw.education\n", "Successfully registered openmetrics_smqa for node qw0991smqas002.nsw.education\n", "Successfully registered openmetrics_smqa for node pw0991smqas001.nsw.education\n", "Successfully registered openmetrics_smqa for node pw0991smqas002.nsw.education\n", "Successfully registered openmetrics_smqa for node pw0992smqas002.nsw.education\n", "Successfully registered openmetrics_smqa for node pw0992smqas001.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0991imxs0001.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0992imxs0001.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0991imxs0002.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0992imxs0002.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0992imxr0002.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0992imxr0001.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0991imxr0001.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0991imxr0002.nsw.education\n", "Successfully registered openmetrics_smtp4a for node pl0992imxr0003.nsw.education\n", "Successfully registered openmetrics_smtp4o for node pl0991imxs0003.nsw.education\n", "Successfully registered openmetrics_smtp4o for node pl0991imxs0004.nsw.education\n", "Successfully registered openmetrics_smtp4o for node pl0992imxs0003.nsw.education\n", "Successfully registered openmetrics_smtp4o for node pl0992imxs0004.nsw.education\n", "Successfully registered openmetrics_soapts for node pw0000ruswfs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_soapts for node pw0000metsfs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_soapts for node pw0000regsfs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_soapts for node pw0000rurnfs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_soapts for node pw0000regnfs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_soapts for node pw0000metnfs01.file.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node du-msj-as0.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node du-mdj-as0.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node du-mds-as0.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node du-mss-as0.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node pu-mps-as0.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node pu-mpj-as1.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node pu-mps-as1.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node pu-mpj-as0.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_solman for node pu-mps-as2.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb13.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na14.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na15.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na16.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na17.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb14.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb15.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb16.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb17.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb18.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb19.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb20.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb21.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb22.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1pa07.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1va03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ia01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1fa05.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1wd03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb10.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1pa05.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ga02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ja01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1va00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ta03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ia00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1wd02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ka01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb11.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ka00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb12.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1fa06.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ta00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ja00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1fa03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ta02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1fa02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1fa01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ga03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1oa00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1oa01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb05.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1cs04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ga05.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1pa06.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1pa02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb06.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1pa00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb08.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1va02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1cs01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1cs02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1fa04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1va07.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1fa00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1pa03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ua00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ta01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ua01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb09.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ga04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ga01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1fa07.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1pa04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1pa01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1wd04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1va01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1va04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1cs03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1va05.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1va06.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1wd01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb07.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1ga00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1nb03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na07.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na11.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na06.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na09.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na13.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na05.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na00.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na10.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na08.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0992sp1na12.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0991sp1wd04.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0991sp1wd01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0991sp1wd03.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sp1 for node pl0991sp1wd02.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_spgps for node pw0472spgps001.nsw.education\n", "Successfully registered openmetrics_splnk for node pl0475splnk001.nsw.education\n", "Successfully registered openmetrics_splnk for node pl0475splnk002.nsw.education\n", "Successfully registered openmetrics_splnk for node pl0991splnk080.nsw.education\n", "Successfully registered openmetrics_splnk for node pl0992splnk080.nsw.education\n", "Successfully registered openmetrics_splnk for node pl0992splf0001.nsw.education\n", "Successfully registered openmetrics_splnk for node pl0991splf0001.nsw.education\n", "Successfully registered openmetrics_splnk for node pl0991splf0002.nsw.education\n", "Successfully registered openmetrics_splnk for node pl0991splnk099.nsw.education\n", "Successfully registered openmetrics_splnkc for node pl0991splnkc003.nsw.education\n", "Successfully registered openmetrics_splnkc for node pl0991splnkc004.nsw.education\n", "Successfully registered openmetrics_splnkc for node pl0992splnkc01.nsw.education\n", "Successfully registered openmetrics_splnkc for node pl0992splnkc02.nsw.education\n", "Successfully registered openmetrics_splnkc for node pl0991splnkc002.nsw.education\n", "Successfully registered openmetrics_splnkc for node pl0992splnkc03.nsw.education\n", "Successfully registered openmetrics_splt for node pu0992splt0001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_spsr for node pw0000spaph001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node cl0991spwl01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node dl0991spwl11.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node dl0991spwl12.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node du-spwl-11.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node du-spwl-12.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node cu-spwl-11.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node pu-spwl-12.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node pu-spwl-11.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node pu-spwl-21.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_spwl for node pu-spwl-22.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb13.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na11.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0va05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0fa03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ga03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ga04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0pa03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0pa07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0va02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0wd04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na08.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0va03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ka00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ua01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ta02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ka01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ga05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0va07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0fa02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ua00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0fa05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0cs03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb12.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb08.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0pa02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ga02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0fa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0pa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na09.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0wd01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb09.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0fa04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ja01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na13.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0oa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ga01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ga00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb10.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ta01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0wd03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0fa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na10.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0va06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb11.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0fa06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ta03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0va01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0fa07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0va00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0wd02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0pa04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ja00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0va04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0ta00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0nb04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0pa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na12.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0pa06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0cs04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0na07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0pa05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0cs02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0cs01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq0 for node ql0991sq0oa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2nb03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2ga01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2nb00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2cs01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2wd03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2cs02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2cs03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2fa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2wd04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2pa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2wd01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2wd02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2cs04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2na01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2nb02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2na00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2ga00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2nb01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2fa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq2 for node ql0992sq2pa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb08.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb09.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb10.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb11.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb12.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb13.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7wd01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7cs03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7ta00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7fa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7ga00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7va00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7ua00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7oa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7wd02.apps.pre.mgmt.det\n", "Successfully registered openmetrics_sq7 for node ql0992sq7cs01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7cs02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7pa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7nb01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7na01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq7 for node ql0992sq7na00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8fa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8fa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na08.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na09.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na10.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na11.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na12.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na13.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb13.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb14.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb15.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb16.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb17.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb18.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb19.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb20.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb21.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb22.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na14.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na15.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na16.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na17.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8pa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8cs03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8pa03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8cs02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb10.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb09.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8wd01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8wd04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8wd03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8cs01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8pa02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb12.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8pa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8wd02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb08.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb11.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8cs04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8nb02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sq8 for node ql0992sq8na00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node du-siq-vs0.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb13.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaga04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaga05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqafa07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana12.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqacs04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqava04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana10.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqapa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqacs01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqawd04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqapa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqava07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaoa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqava06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaka00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqapa05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqacs02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaoa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqafa03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqafa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqava03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqafa05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana09.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaja00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaua01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaka01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqava01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana11.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqava02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaga00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqafa02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaua00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqata00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqava00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqafa04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqapa02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqava05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaga02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaja01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqawd03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqata01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqapa03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqawd02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqafa06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaga03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqawd01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqacs03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana13.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqaga01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqapa04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqafa01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqana08.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb09.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb07.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb05.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb10.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb11.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb12.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb06.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb08.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqa for node ql0992sqanb03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf019 for node qw0992sqf002n1.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf019 for node qw0991sqf002n2.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf03a for node pw0992sqf003n2.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf03a for node pw0992sqf002n1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf03a for node pw0991sqf003n1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf03a for node pw0991sqf002n2.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf03a for node pw0991sqf003n3.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf03c for node pw0992sqf004n3.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf03c for node pw0992sqf004n1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf03c for node pw0991sqf004n2.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqf101 for node pw0992sqf101n3.nsw.education\n", "Successfully registered openmetrics_sqf101 for node pw0991sqf101n2.nsw.education\n", "Successfully registered openmetrics_sqf101 for node pw0992sqf101n1.nsw.education\n", "Successfully registered openmetrics_sqf102 for node qw0992sqf102n1.nsw.education\n", "Successfully registered openmetrics_sqf102 for node qw0991sqf102n2.nsw.education\n", "Successfully registered openmetrics_sqf104 for node qw0991sqf104n1.nsw.education\n", "Successfully registered openmetrics_sqf105 for node qw0991sqf105n1.nsw.education\n", "Successfully registered openmetrics_sqf105 for node qw0992sqf105n2.nsw.education\n", "Successfully registered openmetrics_sqf106 for node qw0992sqf106n1.nsw.education\n", "Successfully registered openmetrics_sqf106 for node qw0991sqf106n2.nsw.education\n", "Successfully registered openmetrics_sqf106 for node pw0991sqf106n1.nsw.education\n", "Successfully registered openmetrics_sqf107 for node qw0991sqf107n1.nsw.education\n", "Successfully registered openmetrics_sqf107 for node qw0992sqf107n2.nsw.education\n", "Successfully registered openmetrics_sqf107 for node pw0992sqf107n2.nsw.education\n", "Successfully registered openmetrics_sqf107 for node pw0991sqf107n3.nsw.education\n", "Successfully registered openmetrics_sqf107 for node pw0991sqf107n1.nsw.education\n", "Successfully registered openmetrics_sqf108 for node qw0992sqf108n1.nsw.education\n", "Successfully registered openmetrics_sqf108 for node qw0991sqf108n2.nsw.education\n", "Successfully registered openmetrics_sqf110 for node pw0992sqf110n2.nsw.education\n", "Successfully registered openmetrics_sqf110 for node pw0991sqf110n3.nsw.education\n", "Successfully registered openmetrics_sqf110 for node pw0991sqf110n1.nsw.education\n", "Successfully registered openmetrics_sqf111 for node pw0991sqf111n2.nsw.education\n", "Successfully registered openmetrics_sqf111 for node pw0992sqf111n1.nsw.education\n", "Successfully registered openmetrics_sqf111 for node pw0992sqf111n3.nsw.education\n", "Successfully registered openmetrics_sqfc01 for node dw0992sqfc01n1.dev.nsw.education\n", "Successfully registered openmetrics_sqfc01 for node pw0991sqfc01h1.nsw.education\n", "Successfully registered openmetrics_sqfc10 for node dw0992sqfc10n1.dev.nsw.education\n", "Successfully registered openmetrics_sqfc10 for node pw0991sqfc10m1.nsw.education\n", "Successfully registered openmetrics_sqfc10 for node pw0991sqfc10m2.nsw.education\n", "Successfully registered openmetrics_sqfc10 for node pw0991sqfc10m3.nsw.education\n", "Successfully registered openmetrics_sqfc2 for node pw0991sqfc20h1.nsw.education\n", "Successfully registered openmetrics_sqfc2 for node pw0991sqfc21h1.nsw.education\n", "Successfully registered openmetrics_sqfc2 for node pw0991sqfc22h1.nsw.education\n", "Successfully registered openmetrics_sqfc2 for node pw0991sqfc23h1.nsw.education\n", "Successfully registered openmetrics_sqfc2 for node pw0991sqfc24h1.nsw.education\n", "Successfully registered openmetrics_sqfc3 for node pw0991sqfc30h1.nsw.education\n", "Successfully registered openmetrics_sqfc3 for node pw0991sqfc31h1.nsw.education\n", "Successfully registered openmetrics_sqfc3 for node pw0991sqfc32h1.nsw.education\n", "Successfully registered openmetrics_sqfc3 for node pw0991sqfc33h1.nsw.education\n", "Successfully registered openmetrics_sqfc3 for node pw0991sqfc34h1.nsw.education\n", "Successfully registered openmetrics_sqfc3 for node pw0991sqfc35h1.nsw.education\n", "Successfully registered openmetrics_sqfc3 for node pw0991sqfc36h1.nsw.education\n", "Successfully registered openmetrics_sqfc9 for node pw0991sqfc90h1.nsw.education\n", "Successfully registered openmetrics_sqfe01 for node pw0992sqfe01n3.nsw.education\n", "Successfully registered openmetrics_sqfe01 for node pw0991sqfe01n2.nsw.education\n", "Successfully registered openmetrics_sqfe01 for node pw0992sqfe01n1.nsw.education\n", "Successfully registered openmetrics_sqfe02 for node qw0991sqfe02n2.pre.nsw.education\n", "Successfully registered openmetrics_sqfe02 for node qw0992sqfe02n1.pre.nsw.education\n", "Successfully registered openmetrics_sqfe02 for node qw0992sqfe02n3.pre.nsw.education\n", "Successfully registered openmetrics_sqfe02 for node pw0992sqfe02n3.nsw.education\n", "Successfully registered openmetrics_sqfe02 for node pw0992sqfe02n1.nsw.education\n", "Successfully registered openmetrics_sqfe02 for node pw0991sqfe02n2.nsw.education\n", "Successfully registered openmetrics_sqfe03 for node qw0992sqfe03n1.nsw.education\n", "Successfully registered openmetrics_sqfe03 for node qw0991sqfe03n2.nsw.education\n", "Successfully registered openmetrics_sqfe03 for node qw0992sqfe03n3.nsw.education\n", "Successfully registered openmetrics_sqfe03 for node pw0992sqfe03n1.nsw.education\n", "Successfully registered openmetrics_sqfe03 for node pw0991sqfe03n2.nsw.education\n", "Successfully registered openmetrics_sqfe03 for node pw0992sqfe03n3.nsw.education\n", "Successfully registered openmetrics_sqfe04 for node qw0992sqfe04n1.nsw.education\n", "Successfully registered openmetrics_sqfe04 for node qw0992sqfe04n3.nsw.education\n", "Successfully registered openmetrics_sqfe04 for node qw0991sqfe04n2.nsw.education\n", "Successfully registered openmetrics_sqfe05 for node pw0991sqfe05n3.nsw.education\n", "Successfully registered openmetrics_sqfe05 for node pw0991sqfe05n1.nsw.education\n", "Successfully registered openmetrics_sqfe05 for node pw0992sqfe05n2.nsw.education\n", "Successfully registered openmetrics_sqfe06 for node pw0991sqfe06n1.nsw.education\n", "Successfully registered openmetrics_sqfe06 for node pw0991sqfe06n3.nsw.education\n", "Successfully registered openmetrics_sqfe06 for node pw0992sqfe06n2.nsw.education\n", "Successfully registered openmetrics_sqfe07 for node pw0991sqfe07n1.nsw.education\n", "Successfully registered openmetrics_sqfe07 for node pw0991sqfe07n3.nsw.education\n", "Successfully registered openmetrics_sqfe07 for node pw0992sqfe07n2.nsw.education\n", "Successfully registered openmetrics_sqfe09 for node qw0991sqfe09n1.nsw.education\n", "Successfully registered openmetrics_sqfe09 for node qw0992sqfe09n2.nsw.education\n", "Successfully registered openmetrics_sqfe20 for node pw0991sqfe20n3.nsw.education\n", "Successfully registered openmetrics_sqfe20 for node pw0992sqfe20n2.nsw.education\n", "Successfully registered openmetrics_sqfe20 for node pw0991sqfe20n1.nsw.education\n", "Successfully registered openmetrics_sqfe21 for node pw0992sqfe21h1.nsw.education\n", "Successfully registered openmetrics_sqfe25 for node dw0992sqfe25n1.dev.nsw.education\n", "Successfully registered openmetrics_sqfe25 for node qw0992sqfe25n1.pre.nsw.education\n", "Successfully registered openmetrics_sqfe25 for node pw0991sqfe25n3.nsw.education\n", "Successfully registered openmetrics_sqfe25 for node pw0991sqfe25n1.nsw.education\n", "Successfully registered openmetrics_sqfe25 for node pw0992sqfe25n2.nsw.education\n", "Successfully registered openmetrics_sqff for node qw0992sqffswh5.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node qw0991sqffswh5.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0992sqffswh1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0992sqsfswh1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0992sqpfswh1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0991sqpfswh1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0991sqffswh1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0991sqsfswh1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0992sqffswh5.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0991sqsfswh7.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqff for node pw0991sqffswh5.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqffsw for node pw0991sqffswh3.nsw.education\n", "Successfully registered openmetrics_sqffsw for node pw0992sqffswh3.nsw.education\n", "Successfully registered openmetrics_sqfl for node qw0991sqfl02n1.nsw.education\n", "Successfully registered openmetrics_sqfl for node qw0992sqfl03n2.nsw.education\n", "Successfully registered openmetrics_sqfl for node qw0991sqfl03n1.nsw.education\n", "Successfully registered openmetrics_sqfl for node qw0992sqfl01n2.nsw.education\n", "Successfully registered openmetrics_sqfl for node qw0991sqfl01n1.nsw.education\n", "Successfully registered openmetrics_sqfl for node qw0992sqfl02n2.nsw.education\n", "Successfully registered openmetrics_sqld for node pw0000sqlpe002.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlfsw for node dw0992sqpfswh2.nsw.education\n", "Successfully registered openmetrics_sqlfsw for node dw0991sqpfswh2.nsw.education\n", "Successfully registered openmetrics_sqlp for node dw0991sqlp0001.nsw.education\n", "Successfully registered openmetrics_sqlp11 for node dw0000lssql001.dbs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node dw0000lssql002.dbs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node dw0991sqp001n1.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node qw0992sqs002n1.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node qw0991sqs001n1.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node qw0000sqlqe015.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node tw0992sqp002n1.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node pw0992sqf001n2.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node pw0000sqlpe128.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node pw0991sqf001n1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node pw0000sqlpm024.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node pw0992sqs701n2.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node pw0991sqs701n3.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlp11 for node pw0991sqs701n1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlq4b for node qw0000sqlqe009.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlq4b for node pw0000sqlpe111.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sqlq7c for node qw0991sqlqm005.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqls for node pw0991sqs101n2.nsw.education\n", "Successfully registered openmetrics_sqls for node pw0992sqs101n1.nsw.education\n", "Successfully registered openmetrics_sqls for node pw0992sqs101n3.nsw.education\n", "Successfully registered openmetrics_sqls for node pw0992sqsfswh2.nsw.education\n", "Successfully registered openmetrics_sqlt for node tw0000sqlte004.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_sqmg for node pw0991sqmgmth0.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_sqmg for node pw0991sqmgmth7.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_sqmgmt for node dw0992sqmgmth1.dev.nsw.education\n", "Successfully registered openmetrics_sqmgmt for node qw0472sqmgmth9.pre.nsw.education\n", "Successfully registered openmetrics_sqmgmt for node pw0991sqmgmth1.nsw.education\n", "Successfully registered openmetrics_sqmgmt for node pw0472sqmgmth9.nsw.education\n", "Successfully registered openmetrics_sqn for node ql0992sqnna01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnna00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnnb02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnwd03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqncs01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnnb03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqncs04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqncs03.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnfa00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnnb01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnwd04.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnwd02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqncs02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnwd01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnnb00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqn for node ql0992sqnga00.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp03b for node pw0992sqp007n2.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp03b for node pw0000sqlpm004.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp03b for node pw0991sqp007n1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp03b for node pw0991sqp007n3.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp03b for node pw0000sqlpm001.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp078 for node qw0991sqp001n1.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp078 for node qw0991sqp002n2.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp07a for node dw0991sqp003n4.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sqp101 for node dw0992sqp101n1.nsw.education\n", "Successfully registered openmetrics_sqp101 for node dw0992sqp101n3.nsw.education\n", "Successfully registered openmetrics_sqp101 for node dw0991sqp101n2.nsw.education\n", "Successfully registered openmetrics_sqp101 for node qw0992sqp101n1.nsw.education\n", "Successfully registered openmetrics_sqp101 for node qw0991sqp101n2.nsw.education\n", "Successfully registered openmetrics_sqp102 for node qw0991sqp102n1.nsw.education\n", "Successfully registered openmetrics_sqp102 for node qw0992sqp102n2.nsw.education\n", "Successfully registered openmetrics_sqp103 for node dw0991sqp103n1.nsw.education\n", "Successfully registered openmetrics_sqp103 for node tw0991sqp103n1.nsw.education\n", "Successfully registered openmetrics_sqp104 for node dw0992sqp104n1.nsw.education\n", "Successfully registered openmetrics_sqp106 for node tw0991sqp106n1.nsw.education\n", "Successfully registered openmetrics_sqp108 for node tw0991sqp108n1.nsw.education\n", "Successfully registered openmetrics_sqp108 for node pw0992sqp108n2.nsw.education\n", "Successfully registered openmetrics_sqp108 for node pw0991sqp108n3.nsw.education\n", "Successfully registered openmetrics_sqp108 for node pw0991sqp108n1.nsw.education\n", "Successfully registered openmetrics_sqp109 for node dw0992sqp109n1.nsw.education\n", "Successfully registered openmetrics_sqp109 for node dw0472sqp10901.nsw.education\n", "Successfully registered openmetrics_sqp109 for node qw0472sqp10901.nsw.education\n", "Successfully registered openmetrics_sqp109 for node tw0991sqp109n1.nsw.education\n", "Successfully registered openmetrics_sqp109 for node tw0472sqp10901.nsw.education\n", "Successfully registered openmetrics_sqp109 for node pw0991sqp109n2.nsw.education\n", "Successfully registered openmetrics_sqp109 for node pw0992sqp109n1.nsw.education\n", "Successfully registered openmetrics_sqp109 for node pw0992sqp109n3.nsw.education\n", "Successfully registered openmetrics_sqp109 for node pw0472sqp109n1.nsw.education\n", "Successfully registered openmetrics_sqp109 for node pw0472sqp109n3.nsw.education\n", "Successfully registered openmetrics_sqp109 for node pw0472sqp109n2.nsw.education\n", "Successfully registered openmetrics_sqp109 for node pw0472sqp10901.nsw.education\n", "Successfully registered openmetrics_sqp111 for node dw0992sqp111n1.nsw.education\n", "Successfully registered openmetrics_sqp112 for node dw0991sqp112n1.nsw.education\n", "Successfully registered openmetrics_sqp112 for node tw0991sqp112n1.nsw.education\n", "Successfully registered openmetrics_sqp113 for node tw0992sqp113n1.nsw.education\n", "Successfully registered openmetrics_sqp114 for node tw0991sqp114n1.nsw.education\n", "Successfully registered openmetrics_sqp114 for node tw0992sqp114h1.nsw.education\n", "Successfully registered openmetrics_sqp115 for node tw0992sqp115n1.nsw.education\n", "Successfully registered openmetrics_sqp116 for node tw0991sqp116n1.nsw.education\n", "Successfully registered openmetrics_sqp118 for node tw0991sqp118n1.nsw.education\n", "Successfully registered openmetrics_sqp119 for node tw0992sqp119n1.nsw.education\n", "Successfully registered openmetrics_sqp120 for node dw0991sqp120n1.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node dw0991sqpe01n3.dev.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node dw0991sqpe01n1.dev.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node qw0992sqpe01n1.pre.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node qw0991sqpe01n2.pre.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node qw0992sqpe01n3.pre.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node tw0992sqpe01n1.test.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node tw0992sqpe01n3.test.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node tw0991sqpe01n2.test.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node pw0992sqpe01n1.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node pw0992sqpe01n3.nsw.education\n", "Successfully registered openmetrics_sqpe01 for node pw0991sqpe01n2.nsw.education\n", "Successfully registered openmetrics_sqpe20 for node tw0991sqpe20n1.nsw.education\n", "Successfully registered openmetrics_sqpe21 for node tw0992sqpe21n1.nsw.education\n", "Successfully registered openmetrics_sqpfsw for node pw0991sqpfswh3.nsw.education\n", "Successfully registered openmetrics_sqpfsw for node pw0992sqpfswh3.nsw.education\n", "Successfully registered openmetrics_sqpl for node tw0472sqpl0001.test.nsw.education\n", "Successfully registered openmetrics_sqs0 for node dw0992sqp002n1.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sqs0 for node qw0992sqs001n2.dbs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_sqs0 for node tw0991sqp001n1.dbs.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sqs0 for node pw0992sqs001n2.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqs0 for node pw0991sqs001n1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqs038 for node pw0992sqs006n3.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqs038 for node pw0992sqs006n1.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqs038 for node pw0991sqs006n2.dbs.det.nsw.edu.au\n", "Successfully registered openmetrics_sqs103 for node qw0991sqs103n1.nsw.education\n", "Successfully registered openmetrics_sqs103 for node qw0992sqs103n2.nsw.education\n", "Successfully registered openmetrics_sqs104 for node pw0991sqs104n2.nsw.education\n", "Successfully registered openmetrics_sqs104 for node pw0992sqs104n1.nsw.education\n", "Successfully registered openmetrics_sqs104 for node pw0992sqs104n3.nsw.education\n", "Successfully registered openmetrics_sqs105 for node qw0991sqs105n2.nsw.education\n", "Successfully registered openmetrics_sqs105 for node qw0992sqs105n1.nsw.education\n", "Successfully registered openmetrics_sqs105 for node pw0992sqs105n2.nsw.education\n", "Successfully registered openmetrics_sqs105 for node pw0991sqs105n3.nsw.education\n", "Successfully registered openmetrics_sqs105 for node pw0991sqs105n1.nsw.education\n", "Successfully registered openmetrics_sqs106 for node pw0991sqs106n3.nsw.education\n", "Successfully registered openmetrics_sqs106 for node pw0992sqs106n2.nsw.education\n", "Successfully registered openmetrics_sqs106 for node pw0991sqs106n1.nsw.education\n", "Successfully registered openmetrics_sqs112 for node pw0992sqs112n2.nsw.education\n", "Successfully registered openmetrics_sqs112 for node pw0991sqs112n3.nsw.education\n", "Successfully registered openmetrics_sqs112 for node pw0991sqs112n1.nsw.education\n", "Successfully registered openmetrics_sqs114 for node qw0992sqs114h1.nsw.education\n", "Successfully registered openmetrics_sqs114 for node pw0991sqs114h1.nsw.education\n", "Successfully registered openmetrics_sqs114 for node pw0991sqs114h2.nsw.education\n", "Successfully registered openmetrics_sqs115 for node pw0992sqs115h1.nsw.education\n", "Successfully registered openmetrics_sqs116 for node qw0472sqs116n1.nsw.education\n", "Successfully registered openmetrics_sqs116 for node qw0472sqs116n2.nsw.education\n", "Successfully registered openmetrics_sqs116 for node tw0472sqs116n1.test.nsw.education\n", "Successfully registered openmetrics_sqs116 for node pw0472sqs116n1.nsw.education\n", "Successfully registered openmetrics_sqs116 for node pw0472sqs116n2.nsw.education\n", "Successfully registered openmetrics_sqs117 for node qw0472sqs117h1.pre.nsw.education\n", "Successfully registered openmetrics_sqs118 for node qw0472sqs118h1.pre.nsw.education\n", "Successfully registered openmetrics_sqs118 for node pw0472sqs118h1.nsw.education\n", "Successfully registered openmetrics_sqse02 for node qw0472sqse02n1.nsw.education\n", "Successfully registered openmetrics_sqse02 for node qw0472sqse02n2.nsw.education\n", "Successfully registered openmetrics_sqse02 for node pw0472sqse02n1.nsw.education\n", "Successfully registered openmetrics_sqse02 for node pw0472sqse02n2.nsw.education\n", "Successfully registered openmetrics_sqsfsw for node pw0992sqsfswh3.nsw.education\n", "Successfully registered openmetrics_sqsfsw for node pw0991sqsfswh3.nsw.education\n", "Successfully registered openmetrics_srpa for node dw0992srpa0001.nsw.education\n", "Successfully registered openmetrics_srpa for node pw0992srpa0001.nsw.education\n", "Successfully registered openmetrics_srpa for node pw0992srpa0002.nsw.education\n", "Successfully registered openmetrics_srpa for node pw0991srpa0002.nsw.education\n", "Successfully registered openmetrics_srpa for node pw0991srpa0001.nsw.education\n", "Successfully registered openmetrics_ss0 for node dl0992ss0va00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0cs03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0ta00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0cs01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0oa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0cs02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0pa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0wd03.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0wd04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0wd01.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0cs04.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0ga00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0na00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0wd02.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_ss0 for node dl0992ss0fa00.apps.dev.det.nsw.edu.au\n", "Successfully registered openmetrics_sshr for node pw0000sshrwts1.nsw.education\n", "Successfully registered openmetrics_sshr for node pw0000astpcrs1.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_sshr for node pw0991sshrwts1.nsw.education\n", "Successfully registered openmetrics_ssis for node qw0000ssisqe01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_ssis for node qw0000ssisqe02.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_ssis for node pw0000ssispe01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0wd02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0cs04.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0na01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0ga00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0cs02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0nb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0wd03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0wd04.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0wd01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0ta00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0pa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0nb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0nb03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0fa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0cs03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0oa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0ka00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0ua00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0na00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0cs01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0va00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0nb00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st0 for node tl0991st0ja00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1nb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1nb03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1ja00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1va00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1wd02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1ua00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1na01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1wd04.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1na00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1cs03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1pa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1fa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1wd01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1ka00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1oa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1cs02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1ga00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1cs01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1nb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1cs04.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1ta00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1nb00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st1 for node tl0991st1wd03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2cs04.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2na01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2wd01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2wd02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2nb03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2na00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2wd04.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2fa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2ga00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2nb02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2cs03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2nb00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2cs01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2cs02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2nb01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st2 for node tl0991st2wd03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7oa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7na00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7pa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7cs02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7ua00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7fa00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7cs01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7wd01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7nb00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7na01.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7va00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7cs03.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7ga00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7ta00.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_st7 for node tl0991st7wd02.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_stmg for node dl0992stmgsntest8.nsw.education\n", "Successfully registered openmetrics_stmg for node pw0991stmgut01.nsw.education\n", "Successfully registered openmetrics_stmg for node pw0992stmgut01.nsw.education\n", "Successfully registered openmetrics_stmg for node pl0992stmgsn01.nsw.education\n", "Successfully registered openmetrics_stmg for node pl0991stmgbs01.nsw.education\n", "Successfully registered openmetrics_stpcc for node pw0472stpcc001.nsw.education\n", "Successfully registered openmetrics_stpcc for node pw0472stpcc002.nsw.education\n", "Successfully registered openmetrics_swrepo for node pw0991swrepo01.nsw.education\n", "Successfully registered openmetrics_swrepo for node pw0992swrepo02.nsw.education\n", "Successfully registered openmetrics_swstes for node dl0475swstes01.nsw.education\n", "Successfully registered openmetrics_swstes for node tl0475swstest01.nsw.education\n", "Successfully registered openmetrics_syslog for node dl0991syslog01.nsw.education\n", "Successfully registered openmetrics_syslog for node pl0991syslog01.nsw.education\n", "Successfully registered openmetrics_syslog for node pl0992syslog01.nsw.education\n", "Successfully registered openmetrics_syss for node tl0991syss0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_syss for node pl0991syss0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_syss for node pl0992syss0001.netmon.det.nsw.edu.au\n", "Successfully registered openmetrics_t4sc for node qw0000t4scm001.vdr.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_t4sc for node tw0000t4scm001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_t4sc for node pw0000t4scm001.vdr.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tedbd001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tcdnd001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tedbs001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tcdnd002.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tagf0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tamc0002.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tcdnd003.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tadb0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tcdnd004.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992tamc0001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0992ttdbd001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0991tedbd001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node tu0991tedbs001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tcdnd003.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tcdnd001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tcdnd002.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992ttdbs001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tamc0001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tedbd001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tcdnd006.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tcdnd004.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tadb0001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tedbs001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992ttdbd001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tcdnd007.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tamc0002.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tagf0001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tcdnd005.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tarp0001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0992tcdnd008.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0991tedbd001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tcdn for node pu0991tedbs001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_tifv for node pw0000tifvms01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_tism for node pw0000tismtm01.appu.det.nsw.edu.au\n", "Successfully registered openmetrics_tm1a for node tw0000tm1ap001.apps.test.det.nsw.edu.au\n", "Successfully registered openmetrics_tm1a for node pw0000tm1ap001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node dw0992util0501.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node dw0992util0301.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node dw0992util0201.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node qw0992util0101.ad.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node qw0992util0301.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node qw0992util0201.svcs.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node qw0992util0501.messaging.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node tw0992util0301.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node tw0992util0201.svcs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node tw0992util0501.messaging.test.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node tw0992util0101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0992util0102.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0992util0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0992util0103.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0992util0104.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0991util0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0991util0102.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0992util0301.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0992util0201.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0992util0202.svcs.det.nsw.edu.au\n", "Successfully registered openmetrics_util for node pw0992util0501.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_vcutil for node tw0991vcutil01.nsw.education\n", "Successfully registered openmetrics_vcutil for node pw0991vcutil01.nsw.education\n", "Successfully registered openmetrics_vcutil for node pw0992vcutil02.nsw.education\n", "Successfully registered openmetrics_vdibk for node pw0991vdibk001.nsw.education\n", "Successfully registered openmetrics_viut for node qw0000viutl001.vi.det.nsw.edu.au\n", "Successfully registered openmetrics_vlbe for node qw0000vlbews01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_vlbe for node pw0000vlbews01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_vlfe for node qw0000vlfeis01.apps.pre.det.nsw.edu.au\n", "Successfully registered openmetrics_vlfe for node pw0000vlfeis01.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_wacd for node pw0991wacd0001.nsw.education\n", "Successfully registered openmetrics_wacd for node pw0992wacd0001.nsw.education\n", "Successfully registered openmetrics_wamd for node pw0992wamd0001.nsw.education\n", "Successfully registered openmetrics_wamd for node pw0991wamd0001.nsw.education\n", "Successfully registered openmetrics_wame for node tw0992wame0001.nsw.education\n", "Successfully registered openmetrics_wclas for node dw0000sqlde002\n", "Successfully registered openmetrics_wclas for node dw0000ewdbpoc1.dbs.test.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node dw0991rwdc1103.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node tw0991wpoc1605UNKNOWN_NO_NIC_INFO\n", "Successfully registered openmetrics_wclas for node tw0991cacr0101.pre.mgmt.det\n", "Successfully registered openmetrics_wclas for node tw0991util0601.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0000sqlpe126\n", "Successfully registered openmetrics_wclas for node pw0000e4agw0003\n", "Successfully registered openmetrics_wclas for node pw0992rwdchu01.ad.mgmt.det\n", "Successfully registered openmetrics_wclas for node pw0992evnt0102.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0992ex19edg01.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0992ex19edg02.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0992ex19edg03.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0992ex19edg04.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0992ex19edg05.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0991evnt0102.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0991ex19edg05.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0991ex19edg04.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0991ex19edg02.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0991ex19edg01.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0991ex19edg03.messaging.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node pw0991caro9902.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_wclas for node PW0991RWDCX001.ad.mgmt.det\n", "Successfully registered openmetrics_web0 for node tw0992web00101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_web0 for node tw0991web00101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_web0 for node tw0991webc0101.ad.test.det.nsw.edu.au\n", "Successfully registered openmetrics_web0 for node pw0992web00101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_web0 for node pw0991webc0101.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_web0 for node pw0992web00701.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_web0 for node pw0991web00701.ad.det.nsw.edu.au\n", "Successfully registered openmetrics_wlceds for node pw0991wlceds01.nsw.education\n", "Successfully registered openmetrics_wlceds for node pw0991wlceds02.nsw.education\n", "Successfully registered openmetrics_wpmp for node pw0991wpmpws01.nsw.education\n", "Successfully registered openmetrics_wpmp for node pw0992wpmpws01.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0991wpoc4001.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc4001.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc1901.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0991wpoc0002.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc1904.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0001.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0991wpoc0001.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0991wpoc1001.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0004.dev.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpocd191.dev.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0008.dev.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0007.dev.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0012.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc2022.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0082.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0002.dev.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0991wpoc0003.dev.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0472wpoc0002.dev.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0991wpoc0004.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0005.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0991wpoc0034.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0991wpoc0035.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0035.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0003.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0501.dev.nsw.education\n", "Successfully registered openmetrics_wpoc for node dl0991wpoc0001.nsw.education\n", "Successfully registered openmetrics_wpoc for node qw0992wpoc1901.pre.nsw.education\n", "Successfully registered openmetrics_wpoc for node qw0992wpoc0001.nsw.education\n", "Successfully registered openmetrics_wpoc for node qw0991wpoc0001.nsw.education\n", "Successfully registered openmetrics_wpoc for node qw0992wpoc0031.nsw.education\n", "Successfully registered openmetrics_wpoc for node qw0991wpoc0031.nsw.education\n", "Successfully registered openmetrics_wpoc for node ql0992wpoc0001.pre.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0991wpoc1001.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0992wpoc2019.test.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0991wpoc2019.test.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0992wpoce816.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0991wpoce816.test.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0991wpoce819.test.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0992wpoce819.test.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0991wpoc0001.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0992wpoc0031.nsw.education\n", "Successfully registered openmetrics_wpoc for node tw0991wpoc0031.nsw.education\n", "Successfully registered openmetrics_wpoc for node tl0991wpoc0001.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0991wpoc0011.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0992wpoc0011.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0991wpoc0002.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0992wpoce819.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0992wpoce816.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0991wpoc0003.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0991wpoc0004.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0991wpoc0034.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0991wpoc0033.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0472wpoc0002.nsw.education\n", "Successfully registered openmetrics_wpoc for node dw0992wpoc0083.nsw.education\n", "Successfully registered openmetrics_wpoc for node pw0991cacr0104.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_wpoc for node pw0992cacr0104.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_wpoc for node pl0991wpoc0001.nsw.education\n", "Successfully registered openmetrics_wscan for node dw0991wscan001.dev.nsw.education\n", "Successfully registered openmetrics_wscan for node pw0991wscan002.nsw.education\n", "Successfully registered openmetrics_wscan for node pw0991wscan001.nsw.education\n", "Successfully registered openmetrics_wsus for node qw0000wsus0001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_wsus for node pw0992wsus0001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_wsus for node pw0991wsus0001.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_wsus83 for node pw0991wsus0102.hbm.det.nsw.edu.au\n", "Successfully registered openmetrics_wsus95 for node pw0000wsusr001.apps.det.nsw.edu.au\n", "Successfully registered openmetrics_wsuse for node tw0472wsuse001.nsw.education\n", "Successfully registered openmetrics_wsuse for node pw0472wsuse001.nsw.education\n", "Successfully registered openmetrics_zkps for node pu0991zkps0001.apps.det.nsw.edu.au\n"]}], "source": ["# Update this api_url with your own Cirrus access token\n", "def main():\n", "    fqdn_list = get_hostnames_from_api(api_url)\n", "    for hostname, dns, dep_env, app_id, support_team, op_window, vm_loc, manager,win_domain in fqdn_list:\n", "        fqdn = f\"{hostname}{dns}\"  \n", "        # Disabled curl to Telegraf /metrics endpoint as we ingest all \"active\" vms in cirrus\n", "        #if test_port_connection(fqdn, port):\n", "            #print(f\"Port {port} is open on {fqdn}\")\n", "            #perform_http_request(fqdn, port, dep_env, app_id, support_team, op_window, vm_loc, manager,win_domain)\n", "        successful_requests.append({\n", "            'service_id': f'openmetrics_{app_id}',\n", "            'hostname': fqdn,\n", "            'dep_env': dep_env,\n", "            'app_id': app_id,\n", "            'support_team': support_team,\n", "            'op_window': op_window,\n", "            'vm_loc': vm_loc,\n", "            'os_manager' : manager,\n", "            'win_domain' : win_domain\n", "        })            \n", "        # Disabled port test for now as we consume all \"active\" vms in Cirrus\n", "        # else:\n", "        #     #print(f\"Port {port} is closed on {fqdn}\")\n", "        #     failed_requests.append({\n", "        #         'hostname': fqdn, \n", "        #         'dep_env': dep_env, \n", "        #         'app_id': app_id, \n", "        #         'support_team': support_team, \n", "        #         'op_window': op_window, \n", "        #         'vm_loc': vm_loc,\n", "        #         'win_domain' : win_domain\n", "        #         })\n", "    # # Export successful requests to a YAML file\n", "    # yaml_filename = \"successful_requests.yaml\"\n", "    # with open(yaml_filename, \"w\") as yaml_file:\n", "    #     yaml.dump(successful_requests, yaml_file)\n", "\n", "    df = create_dataframe(successful_requests)\n", "    json_payloads, grouped_app_ids = process_dataframe(df)\n", "    \n", "    register_service_with_consul(json_payloads,consul_url,consul_token)\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 223, "metadata": {}, "outputs": [], "source": ["#export_files(json_payloads, grouped_app_ids) # output files can be created here.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}
// obs-col prod - prometheus-LTS.nomad job definition

// Long term storage instance - resides on obscol02
// to allow concurrent 'prometheus' to run.
// Refer Jedd or ROMEO-764     (2021-07)

variables {
  image_prometheus = "quay.education.nsw.gov.au/observability/prometheus:prod-obscol"
}


job "prometheus-appid-generic" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"

  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "prometheus-appid-generic" {
    network {
      port "http" {
//        static = 9090
      }
    }

    volume "prometheus"  {
      type = "host"
      source = "prometheus"
      read_only = false
    }

    task "prometheus-appid-generic" {
      driver = "docker"

      volume_mount {
        volume = "prometheus"
        destination = "/prometheus"
        read_only = false
      }

      config {
        ports = ["http"]
        image = var.image_prometheus
        #dns_servers = ["************"]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }      

        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
          "/opt/sharednfs/prometheus-configuration/prod/prometheus/rules:/etc/prometheus/rules.d"
        ]

        args = [
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_http}",
          "--web.external-url=https://prometheus.obs.nsw.education",
          "--web.page-title=Generic Prometheus on DoE ObsCol PROD cluster",

          "--config.file=/etc/prometheus/prometheus.yml",

          "--enable-feature=agent",

          "--web.console.libraries=/usr/share/prometheus/console_libraries",
          "--web.console.templates=/usr/share/prometheus/consoles",
        ]

      }

      service {
        name = "prometheus-appid-generic"
        port = "http"

        check {
          type = "http"
          port = "http"
          path = "/-/healthy"
          interval = "20s"
          # 2022-10-31 jedd - changed timeout from10s to 2m as startup can be quite slow
          timeout = "2m"
        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.prometheus-appid-generic.rule=Host(`prometheus-appid-generic.obs.nsw.education`)",
          "traefik.http.routers.prometheus-appid-generic.tls=false",
          "traefik.http.routers.prometheus-appid-generic.entrypoints=http,https",
        ]

        meta {
          cir_app_id = "obs"
          env = "prod"
          cluster = "obscol"
        }
      }


      template {
        data = <<EOH
global:
  external_labels:
    nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: obscol-prometheus-appid-generic
    env: prod
# Do not add this label it will cause duplicate series every time a prometheus scraper restarts
#    nomad_alloc_id: {{ env "NOMAD_ALLOC_ID" }}

scrape_configs:
  # ObsCol instance - monitoring this job by itself effectively.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['prometheus-appid-generic.obs.nsw.education']

###    # 2022-10-21 T 0745 jedd - disabling grafana_metrics and confluence metrics entirely
###    # 2022-05-23 jedd - Jeffrey enabled this plugin and provide a token - refer Vault /Confluence or Consul k/v
###    - job_name: 'confluence'
###      scheme: https 
###      proxy_url: "http://proxy.det.nsw.edu.au:80"
###      metrics_path: '/plugins/servlet/prometheus/metrics'
###      params:
###        token: [ '{{ key "confluence/token" }}' ]
###      static_configs:
###        # Note we can't hit the hosts directly, only the LB'd front end
###        - targets: ['confluence.education.nsw.gov.au']
###      # 2022-10-20 - jedd - reducing the 12k confluence metrics by dropping these surpluse metrics:
###      #     confluence_plugin_enabled_count
###      #     confluence_request_duration_on_path_bucket
###      #     confluence_request_duration_on_path_count
###      #     confluence_request_duration_on_path_sum       
###      #     confluence_user_failed_login_count
###      #     confluence_user_login_count
###      #     confluence_user_logout_count
###      metric_relabel_configs:
###      - source_labels: [__name__]
###        # regex: confluence_request_duration_on_path_bucket
###        regex: confluence_(request_duration_on_path_bucket|request_duration_on_path_count|request_duration_on_path_sum|user_failed_login_count|user_login_count|user_logout_count|plugin_enabled_count)
###        action: drop



###    - job_name: 'oracle_metrics'
###      static_configs:
###        - targets: ['pl0992obscol02.nsw.education:9161']
###      metrics_path: /metrics
###  
###    # 2022-10-21 T 0745 jedd - disabling grafana_metrics and confluence metrics entirely
###    - job_name: 'grafana_metrics'
###      scheme: 'https'
###      tls_config:
###        insecure_skip_verify: true
###      metrics_path: /metrics
###      static_configs:
###        - targets: ['grafana.mtm.apps.det.nsw.edu.au']
###      # 2022-10-20 - jedd - reducing the 3405 grafan metrics by dropping these excessively verbose items
###      #     grafana_http_request_duration_seconds_bucket         (1248)
###      #     grafana_datasource_request_duration_seconds_bucket    (510)
###      #     grafana_http_request_duration_seconds_count            (96)
###      #     grafana_datasource_request_duration_seconds_count      (34)
###      #     grafana_datasource_request_duration_seconds_sum        (34)
###      metric_relabel_configs:
###      - source_labels: [__name__]
###        regex: grafana_(datasource_request_duration_seconds_bucket|datasource_request_duration_seconds_count|datasource_request_duration_seconds_sum|http_request_duration_seconds_bucket|http_request_duration_seconds_count)
###        action: drop


  # 2021-12-01 jedd - static until we get this bedded down
  # native confluent metrics (rather than the API method used by the ccloud nomad job)
  - job_name: Confluent Cloud
    scrape_interval: 2m
    scrape_timeout: 1m
    honor_timestamps: true
    static_configs:
      - targets:
        - api.telemetry.confluent.cloud
    scheme: https
    basic_auth:
      username: "{{ key "ccloud/api-key" }}"
      password: "{{ key "ccloud/api-secret" }}"
    proxy_url: "http://proxy.det.nsw.edu.au:80"
    metrics_path: /v2/metrics/cloud/export
    params:
      "resource.kafka.id":
        # DEV-XFI
        # Kafka: lkc-1k503
        # Schema Registry: lsrc-qqz9d
        # Connector: lcc-2zy52
        - lkc-1k503

        # Production cluster (2021-12)
        # Kafka :lkc-73zyj
        # Schema registry : lsrc-o0y3p
        # Connector : lcc-zokzd
        - lkc-73zyj

        # Test
        # Kafka: lkc-z1nrd
        # Schema Registry: lsrc-0vvkq
        # Connector: lcc-11ry5
        - lkc-z1nrd

        # Pre-Prod
        # Kafka : lkc-11qw6
        # Schema registry: lsrc-3ojnw
        # Connector: lcc-ggk0r
        - lkc-11qw6


  # ping targets are ICMP only (no agent) using blackbox exporter:
  # eg. http://blackbox.obs.nsw.education:9115/probe?target=__address__&type=icmp
  - job_name: 'ping_member_servers_sd'
    scrape_interval: 1m
    metrics_path: /probe
    params:
      module: ["icmp"]
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['pingtarget']
    relabel_configs:
      - source_labels: [__meta_consul_address]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: blackbox.service.dc-cir-un-prod.collectors.obs.nsw.education:9115

  ## Any external node that runs openmetrics (migrated from 'telegraf' 2021-07-30)
  #- job_name: 'openmetrics'
  #  consul_sd_configs:
  #    - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
  #      datacenter: 'dc-cir-un-prod'
  #      token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
  #      services: ['openmetrics']
  #  relabel_configs:
  #    - source_labels: [__meta_consul_metadata_cir_app_id]
  #      target_label: cir_app_id
  #    - source_labels: [__meta_consul_metadata_cir_app_id]
  #      target_label: app_id          
  #    - source_labels: [__meta_consul_metadata_env]
  #      target_label: env
  #    - source_labels: [__meta_consul_service_metadata_domain]
  #      target_label: domain
  #    - source_labels: [__meta_consul_service_metadata_metrics_path]
  #      target_label: __metrics_path__
  #      regex: '(.+)'  # Do not perform the replace if there was no metrics path



  # Any standalone exporter that lives in the nomad cluster and not the agent
  - job_name: 'prometheus-exporter'
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['prometheus-exporter']

  - job_name: 'nifi'
    metrics_path: /metrics/
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['nifi']

  - job_name: 'ssl'
    metrics_path: /probe
    params:
      module: ["https"]
    consul_sd_configs:
      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
        datacenter: 'dc-cir-un-prod'
        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
        services: ['https']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: exporter-ssl.service.dc-cir-un-prod.collectors.obs.nsw.education:9219


#    relabel_configs:
#      - source_labels: ['__meta_consul_tags']
#        regex: '(.*)http(.*)'
#        action: keep

#rule_files:
#  - /etc/prometheus/rules.d/*.rules
#  - /etc/prometheus/rules.d/*.yaml
#  - /etc/prometheus/rules.d/*.yml

#alerting:
#  alertmanagers:
#    - consul_sd_configs:
#      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
#        datacenter: 'dc-cir-un-prod'
#        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
#        services: ['alertmanager']


remote_write:
- name: mimir
  url: "https://mimir-rwb-write.obs.nsw.education/api/v1/push"
  headers: 
    X-Scope-OrgID: prod
  tls_config:
    insecure_skip_verify: true
#disabling to conserve metric ingest to GrafanaCloud 24-01-2022 James
#- name: grafanacloud
#  url: https://prometheus-prod-09-prod-au-southeast-0.grafana.net/api/prom/push
#  proxy_url: http://proxy.det.nsw.edu.au:80
#  basic_auth:
#    username: 768052
#    password: ********************************************************************************************************************************************************  
      
EOH
        destination = "local/prometheus.yaml"
      }

      resources {
        cpu    = 150
        memory = 1000
        memory_max = 16000
      }

    }
  }
}

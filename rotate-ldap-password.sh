#!/bin/bash
# LDAP Password Rotation Script
# This script retrieves LDAP credentials from Vault, rotates the password,
# and stores the new password back in Vault.

set -e

# Function to log messages
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to log errors and exit
error_exit() {
  log "ERROR: $1"
  exit 1
}

# Check if vault command is available
if ! command -v vault &> /dev/null; then
  error_exit "Vault CLI is not installed or not in PATH"
fi

# Check if LDAP tools are installed
if ! command -v ldappasswd &> /dev/null; then
  log "Installing LDAP client tools..."
  apt-get update && apt-get install -y ldap-utils || error_exit "Failed to install LDAP tools"
fi

# Retrieve LDAP configuration from Vault
log "Retrieving LDAP configuration from Vault..."
VAULT_LDAP_CONFIG_PATH="secret/ldap/config"
VAULT_LDAP_CREDS_PATH="secret/ldap/service-account"

# Get LDAP configuration
LDAP_HOST=$(vault kv get -field=host $VAULT_LDAP_CONFIG_PATH)
LDAP_PORT=$(vault kv get -field=port $VAULT_LDAP_CONFIG_PATH)
LDAP_BASE_DN=$(vault kv get -field=base_dn $VAULT_LDAP_CONFIG_PATH)

# Get current credentials
LDAP_SERVICE_ACCOUNT=$(vault kv get -field=username $VAULT_LDAP_CREDS_PATH)
LDAP_CURRENT_PASSWORD=$(vault kv get -field=password $VAULT_LDAP_CREDS_PATH)

# Validate required variables
[[ -z "$LDAP_HOST" ]] && error_exit "LDAP host not found in Vault"
[[ -z "$LDAP_PORT" ]] && error_exit "LDAP port not found in Vault"
[[ -z "$LDAP_BASE_DN" ]] && error_exit "LDAP base DN not found in Vault"
[[ -z "$LDAP_SERVICE_ACCOUNT" ]] && error_exit "LDAP service account not found in Vault"
[[ -z "$LDAP_CURRENT_PASSWORD" ]] && error_exit "LDAP current password not found in Vault"

log "LDAP configuration retrieved successfully"
log "Service account: $LDAP_SERVICE_ACCOUNT"

# Generate a new password
# Complex password with uppercase, lowercase, numbers, and special characters
NEW_PASSWORD=$(openssl rand -base64 32 | tr -dc 'a-zA-Z0-9!@#$%^&*()_+' | head -c 24)

log "Generated new password"

# Change the password
log "Changing LDAP password..."
LDAPTLS_REQCERT=never ldappasswd -H ldaps://$LDAP_HOST:$LDAP_PORT \
  -D "$LDAP_SERVICE_ACCOUNT" \
  -w "$LDAP_CURRENT_PASSWORD" \
  -s "$NEW_PASSWORD" \
  "$LDAP_SERVICE_ACCOUNT" || error_exit "Failed to change LDAP password"

# Verify the new password works
log "Verifying new password..."
if LDAPTLS_REQCERT=never ldapsearch -H ldaps://$LDAP_HOST:$LDAP_PORT \
  -D "$LDAP_SERVICE_ACCOUNT" \
  -w "$NEW_PASSWORD" \
  -b "$LDAP_BASE_DN" \
  -s base "objectclass=*" > /dev/null; then
  
  log "Password verification successful"
  
  # Store the new password in Vault
  log "Storing new password in Vault..."
  vault kv put $VAULT_LDAP_CREDS_PATH password="$NEW_PASSWORD" username="$LDAP_SERVICE_ACCOUNT" || \
    error_exit "Failed to store new password in Vault"
  
  log "Password rotation completed successfully"
else
  error_exit "Password verification failed! Manual intervention required."
fi

exit 0

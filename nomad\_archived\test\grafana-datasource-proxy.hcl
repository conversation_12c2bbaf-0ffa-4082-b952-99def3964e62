job "grafana-datasource-proxy" {
  datacenters = ["dc-un-test"]
  type = "service"

  group "proxy" {
    count = 1

    task "proxy" {
      driver = "docker"

      config {
        image = "haproxy:2.3"
        dns_servers = ["************"]  # dnsmasq on container host

        port_map {
          cloudera_http = 7180
        }

        mounts = [
          {
            type = "bind"
            source = "local/haproxy.cfg"
            target = "/usr/local/etc/haproxy/haproxy.cfg"
          }
        ]
      }

      template {
        data = <<EOH
# HAProxy configuration
global
    maxconn 256

defaults
    mode http
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    log global

frontend http-in
    bind *:7180
    default_backend cloudera_mgmt

# Proxy to Cloudera PRE MGMT interface
backend cloudera_mgmt
    server qu0000hdpj2001 qu0000hdpj2001.dbs.pre.mgmt.det:7180 maxconn 32
EOH
        destination = "local/haproxy.cfg"
      }
    }
  }
}

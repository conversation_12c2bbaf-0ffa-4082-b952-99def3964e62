job "metricbeat-remote-write" {
  type = "service"

  group "collector" {
    task "metricbeat" {
      driver = "docker"

      artifact {
        source = "git::https://bitbucket.det.nsw.edu.au/scm/mbd/metricbeat-config.git"
        destination = "metricbeat"
      }

      config {
        image = "https://docker.elastic.co/beats/metricbeat:7.9.2"

        // metricbeat supports label-based declaration of modules, as described in:
        // https://www.elastic.co/guide/en/beats/metricbeat/current/configuration-autodiscover-hints.html
        // These were too inflexible for our setup
        labels {
        }

        volumes = [
          "metricbeat/metricbeat-prometheus.yml:/usr/share/metricbeat/metricbeat.yml:ro",
        ]
      }

      env {

      }

    }
  }
}

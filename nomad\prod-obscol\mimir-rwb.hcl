// Prod Observability Collector - HAC - mimir - read / write / backend 3-division mode

// Refer:  https://grafana.com/docs/mimir/latest/operators-guide/architecture/deployment-modes/#read-write-mode

// Similar to the other modes, each Grafana Mimir process is invoked with its -target parameter set to the 
// specific service: -target=read, -target=write, or -target=backend.

// Pay careful attention to the variables section this needs to align with the nomad cluster

variables {
  image_mimir = "quay.education.nsw.gov.au/observability/mimir:prod-obscol" # mimir 2.10.0
  image_redis = "quay.education.nsw.gov.au/observability/redis:prod-obscol"
  loki_url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
  #jaeger_endpoint = "https://localhost/api/traces"
  mimir_redis_endpoint = "mimir-rwb-redis.obs.nsw.education"
  mimir_read_endpoint = "mimir-rwb-read.obs.nsw.education"
  mimir_write_endpoint = "mimir-rwb-write.obs.nsw.education"
  mimir_backend_endpoint = "mimir-rwb-backend.obs.nsw.education"
  env = "prod"
}

job "mimir-rwb" {
  datacenters = ["dc-cir-un-${var.env}"]
  update {
    max_parallel     = 1
    min_healthy_time = "10s"
    healthy_deadline = "11m"
    progress_deadline = "12m"
    canary = 1
    auto_promote = true
    auto_revert = true
  }

# 2025-06-16 - James: Scaling to 992
   constraint {
     attribute = "${attr.unique.hostname}"
     operator = "regexp"

#     # 2024-09-13 jedd - relocate to AWS
#     # 2025-06-17 james - relocate back to GovDC
#     # value = "pl0992obscol0[123]"
     value = ".*0992.*0[123]"
 }
  
  # READ group  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-read-group" {
    count = 3
    
    # 2025-06-16 - James: ensure task is still a 1:1 on available nodes
    constraint { 
      operator  = "distinct_hosts"
      value     = "true"
    }
    
    network {
      port "http" {}
      port "grpc" {}
    }
    service {
      name = "mimir-rwb-read"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "mimir-rwb-read"
      }

       tags = [
         "traefik.enable=true",
         "traefik.http.routers.mimir-rwb-read.entrypoints=https",
         "traefik.http.routers.mimir-rwb-read.rule=Host(`${var.mimir_read_endpoint}`)",
       ]      

      check {
        name            = "mimir rwb read"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "1s"
      }
    }

    task "mimir-rwb-read" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"
 
      env {
        # 2024-09-13 jedd - relocate to AWS
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        #JAEGER_ENDPOINT = var.jaeger_endpoint
      }

      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc"
        ]
        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=mimir-read"
          }
        }

        args = [
          "-target=read",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      
      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 1000
        memory     = 8000
        memory_max = 13000
      }
    }
  }   # end-READ group


  # WRITE  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-write-group" {
    count = 3 # 2025-06-17 - James scaled down to GovDC
    constraint { 
      operator  = "distinct_hosts"
      value     = "true"
    }

# 2025-05 - James dropped volume mounting in favour of volumes, it's easier
#    volume "vol_mimir"  {
#      type = "host"
#      source = "vol_mimir"
#      read_only = false
#    }

    ephemeral_disk {
      migrate = true
      sticky  = true
      size = 500
    }

    network {
      port "http" {}
      port "grpc" {}
    }

    service {
      name = "mimir-rwb-write"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "write"
        service = "mimir-write"
      }

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-rwb-write.entrypoints=http,https",
        "traefik.http.routers.mimir-rwb-write.rule=Host(`${var.mimir_write_endpoint}`)",
      ]   

      check {
        name            = "mimir rwb write"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "2s"
      }
    }

    task "mimir-rwb-write" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"

      env {
        # 2024-09-13 jedd - relocate to AWS
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        #JAEGER_ENDPOINT = var.jaeger_endpoint
      }

      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc",
        ]

        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=mimir-write"
          }
        }
        volumes = [
            "/opt/localstore/mimir/:/mimir"
        ]
        args = [
          "-target=write",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
      }

      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 2000
        # 2025-05-29 jedd - changed from 10Gb to 20GB - as seeing 17.5GB in Nomad GUI
        memory     = 20000
        memory_max = 48000
      }
    }
  }   # end-WRITE group


  # BACKEND  = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "mimir-rwb-backend" {
    count = 3
    # 2025-06-16 - James: ensure distinct as there are node names in persistant storage still
    constraint { 
      operator  = "distinct_hosts"
      value     = "true"
    }    
#    volume "vol_mimir"  {
#      type = "host"
#      source = "vol_mimir"
#      read_only = false
#    }  

    ephemeral_disk {
      migrate = true
      sticky  = true
    }

    network {
      port "http" {}
      port "grpc" {}
    }
    service {
      name = "mimir-rwb-backend"
      port = "http"

      meta {
        alloc_id  = node.unique.name
        component = "backend"
      }

        tags = [
        "traefik.enable=true",
        "traefik.http.routers.mimir-rwb-backend.entrypoints=https",
        "traefik.http.routers.mimir-rwb-backend.rule=Host(`${var.mimir_backend_endpoint}`)",
    ]      

      check {
        name            = "mimir rwb backend"
        port            = "http"
        protocol        = "http"
        tls_skip_verify = true
        type            = "http"
        path            = "/ready"
        interval        = "5s"
        timeout         = "2s"
      }
    }

    task "mimir-rwb-backend" {
      driver       = "docker"
      user         = "nobody"
      kill_timeout = "90s"
      kill_signal = "SIGTERM"

      env {
        # 2024-09-13 jedd - relocate to AWS
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        #JAEGER_ENDPOINT = var.jaeger_endpoint
      }

#      volume_mount {
#       volume = "vol_mimir"
#       destination = "/mimir"
#       read_only = false
#     }
      
      config {
        image = var.image_mimir

        ports = [
          "http",
          "grpc",
        ]
        logging {
          type = "loki"
          config {
            loki-url = var.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=${var.env},service_name=mimir-backend"
          }
        }

        args = [
          "-target=backend",
          "-config.file=/local/mimir-config.yml",
          "-config.expand-env=true",
        ]
        volumes = [
          "/opt/localstore/mimir/:/mimir"
      ]
      }

      template {
        data = file("assets/mimir-rwb-config.yml")
        destination = "local/mimir-config.yml"
      }

      resources {
        cpu        = 2000
        memory     = 8000
        # 2025-05-29 jedd - changed from 14Gb to 16GB - as seeing 13.3GB in Nomad GUI for one task
        memory_max = 16000
      }
    }
  }   # end-BACKEND group
}

import requests

CONSUL_ADDRESS  = "http://tl0992obscol01.nsw.education:8500"
consul_token = 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'

def get_service_ids():
    """
    Retrieves all service IDs from <PERSON>.
    
    Returns:
        A list of service IDs.
    """
    url = f"{CONSUL_ADDRESS}/v1/agent/services"
    response = requests.get(url,headers={'X-Consul-Token': consul_token})
    services = response.json()
    service_ids = [service for service in services]
    return service_ids

def deregister_service(service_id):
    """
    Deregisters a service by its ID from <PERSON>.
    
    Parameters:
        service_id (str): The ID of the service to deregister.
    """
    url = f"{CONSUL_ADDRESS}/v1/agent/service/deregister/{service_id}"
    response = requests.put(url,headers={'X-Consul-Token': consul_token})
    if response.status_code == 200:
        print(f"Service {service_id} deregistered successfully.")
    else:
        print(f"Failed to deregister service {service_id}. Status code: {response.status_code}")

def deregister_services():
    """
    Retrieves all service IDs and deregisters them.
    """
    service_ids = get_service_ids()
    for service_id in service_ids:
        deregister_service(service_id)

# Example usage
deregister_services()

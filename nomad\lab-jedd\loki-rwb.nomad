
# Loki-rwb - taken from prod-obscol (<PERSON>) take, 2025-04-01

# Uses unique (garage) s3 bucket, single read, single write, but unique hosts
# set for read and write tasks.  Backend is fine at 1. 

# Notes from obscol-prod 2025-04-01 follow, for Loki 3.4.x
#
# <PERSON>' notes / gotchas:
# - RWB is easier with a simplier config, I believe g<PERSON><PERSON><PERSON><PERSON> has set suitable defaults
#
# - Compactor address needs to use the http://loki.obs.nsw.education endpoint
#
# - Backend task needs a router other than loki.obs.nsw.education otherwise reads dont work, 
#   similar to how mimir-rwb is setup in Traefik.
#
# - Flushing ingestors on shutdown to s3 can take minutes -- which causes canary type updates 
#   to fail, as there is still a process accessing files on the NFS mount.
#
# - Read & Write nomad tasks can scale as we need, but backend can remain at 1
#   @TODO need to test in a patching cycle to understand if running multiple backends 
#   in a ring impacts the performance of (f.e.) queries.

# Bucket credentials (garage):
#     Bucket:  loki-rwb
#     Key name: loki-rwb-key
#     Key ID: GK610d2dff11c1e81513b2d9c4
#     Secret key: 0eded06400d5894f17eb209dce00f63e59ff932022c8a04bc77f69bc85959351


# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variable "nomad_dc" {
  type = string
  default = ""
  description = "DC to constrain and run this job - it should be defined in your shell environment"
}



locals {
  image_loki_rwb = "registry.obs.int.jeddi.org/loki:3.4.2"
  consul_master = var.nomad_dc == "DG" ? "dg-pan-01.int.jeddi.org:8500"          : "py-mon-01.int.jeddi.org:8500"
  host_constraint = var.nomad_dc == "DG" ? "dg-hac-0[123456]"                    : "py-hac-0[123]"

  # Log to loki monolithic, at least initially
  loki_url = "https://loki.obs.int.jeddi.org/loki/api/v1/push"
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "loki-rwb" {
  datacenters = [ var.nomad_dc ]

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = local.host_constraint
  }


  # Group  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-rwb-read" {
    count = 2

    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }

    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    network {
      port "http" { }
      port "grpc" { }
    }

    # Task  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "loki-rwb-read" {
      driver = "docker"

      user   = "nobody"

      config {
        image = local.image_loki_rwb

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        ports = [
          "http",
          "grpc",
        ]

        volumes = [
          "/opt/sharednfs/loki-rwb:/loki"
        ]

        args = [
          "-target=read",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          "-log-config-reverse-order",
        ]
      }

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
      }
      
      service {
        name = "loki-rwb-read"
        port = "http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-rwb-read.entrypoints=http,https",
          "traefik.http.routers.loki-rwb-read.rule=Host(`loki-rwb.obs.int.jeddi.org`)",
        ]

        check {
          name     = "Loki read"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

      resources {
        cpu = 100
        memory = 200
        memory_max = 800
      }
    }
  }

# Group  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-rwb-write" {
    count = 2

    constraint {
      operator  = "distinct_hosts"
      value     = "true"
    }
    
    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    network {
      port "http" { }
      port "grpc" { }
    }

    task "loki-rwb-write" {
      driver = "docker"

      user   = "nobody"

      config {
        image = local.image_loki_rwb

        logging {
          type = "loki"

          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }
        
        ports = [
          "http",
          "grpc",
        ]

        volumes = [
          "/opt/sharednfs/loki-rwb:/loki"
        ]

        args = [
          "-target=write",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          "-log-config-reverse-order",
        ]
      }

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
      }

      service {
        name     = "Loki-write"
        port     = "http"
   
        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-rwb-write.entrypoints=https",
          "traefik.http.routers.loki-rwb-write.rule=Host(`loki-rwb.obs.int.jeddi.org`)&& Path(`/loki/api/v1/push`)",
        ]

        check {
          name     = "Loki write"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }
      service {
        name     = "Loki-write-otlp"
        port     = "http"
   
        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-rwb-write-otlp.entrypoints=https",
          "traefik.http.routers.loki-rwb-write-otlp.rule=Host(`loki-rwb.obs.int.jeddi.org`)&& Path(`/otlp/v1/logs`)",
        ]

        check {
          name     = "Loki write-otlp"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

      service {
        name     = "Loki-write-otel"
        port     = "http"
   
        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-rwb-write-otel.entrypoints=https",
          "traefik.http.routers.loki-rwb-write-otel.rule=Host(`loki-rwb-otel.obs.nsw.education`)&& Path(`/loki/api/v1/push`)",
        ]

        check {
          name     = "Loki write-otel"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }


      resources {
        cpu = 100
        memory = 200
        memory_max = 800
      }
    }  // end-task loki-rwb-write
  }  // end-group loki-rwb-write


  # Group  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-backend" {
    # This stays as '1' for backend role
    count = 1

    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    network {
      port "http" { }
      port "grpc" { }
    }

    task "loki-rwb-backend" {
      driver = "docker"

      user   = "nobody"

      config {
        image = local.image_loki_rwb

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME}"
          }
        }

        ports = [
          "http",
          "grpc",
        ]

        volumes = [
          "/opt/sharednfs/loki-rwb:/loki"
        ]

        args = [
          "-target=backend",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          "-log-config-reverse-order",
        ]
      }

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
      }
      
      service {
        name = "loki-rwb-backend"
        port = "http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-rwb-backend.entrypoints=https",
          "traefik.http.routers.loki-rwb-backend.rule=Host(`loki-rwb-backend.obs.int.jeddi.org`)",
        ]

        check {
          name     = "Loki backend"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

      resources {
        cpu = 100
        memory = 200
        memory_max = 800
      }
    }
  }  
}

job "test-dbforbix-oracle12c" {
  datacenters = ["dev"]
  type = "service"

  group "collector" {
    count = 1

    task "jboss" {
      driver = "docker"

      config {
        image = "store/oracle/database-enterprise:12.2.0.1"

        port_map {
          oracle = 1521
        }
      }

      env {
        "DB_SID" = "ORCLCDB"
        "DB_PDB" = "ORCLPDB1"
        "DB_MEMORY" = "2GB"
        "DB_DOMAIN" = "localdomain"
        # Default password Oradoc_db1
      }

      service {
        name = "orcl-integration-test"
        port = "oracle"
      }
    }
  }

}
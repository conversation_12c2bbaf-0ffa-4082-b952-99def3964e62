# Vault policy for LDAP password rotation
# This policy grants the necessary permissions for the LDAP password rotation job

# Allow reading LDAP configuration
path "secret/data/ldap/config" {
  capabilities = ["read"]
}

# Allow reading and updating LDAP service account credentials
path "secret/data/ldap/service-account" {
  capabilities = ["read", "update"]
}

# For Vault KV v2, we also need metadata permissions
path "secret/metadata/ldap/service-account" {
  capabilities = ["read", "update"]
}

# Allow listing the paths
path "secret/metadata/ldap" {
  capabilities = ["list"]
}

path "secret/metadata/ldap/*" {
  capabilities = ["list"]
}

# For Vault KV v1 (if used instead of v2)
# path "secret/ldap/config" {
#   capabilities = ["read"]
# }
# 
# path "secret/ldap/service-account" {
#   capabilities = ["read", "update"]
# }

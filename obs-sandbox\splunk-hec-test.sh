#!/bin/bash

# Get current timestamp in milliseconds
TIMESTAMP=$(date +%s%3N)

# Create the JSON payload
JSON_DATA=$(cat << EOF
{
    "index": "mtm",
    "time": $TIMESTAMP,
    "host": "notify.dev.education.nsw.gov.au",
    "source": "school_notify_dev",
    "sourcetype": "Postman",
    "event": {
        "foo": "Hello world",
        "bar": "Goodbye world"
    }
}
EOF
)

# Make the request
curl -v \
    -X POST \
    -H "Authorization: Splunk 8c337d9a-efe6-4043-b65f-c54a31e2716c" \
    -H "Content-Type: application/json" \
    -d "$JSON_DATA" \
    "https://http-inputs-nswdoe.splunkcloud.com/services/collector"
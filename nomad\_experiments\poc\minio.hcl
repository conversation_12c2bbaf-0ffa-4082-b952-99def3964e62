job "minio" {
  type = "service"
  datacenters = ["dc1"]

  group "minio" {

    task "minio" {
      driver = "docker"

      config {
        image = "https://docker.io/minio/minio:latest"
        port_map {
          minio = 9000
        }

        args = [
          "server", "/data"
        ]
      }


      resources {
        network {
          port "minio" {
            static = 9000
          }
        }
      }

      service {
        name = "minio-http"
        port = "minio"

        check {
          type = "http"
          port = "minio"
          path = "/minio/health/live"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        MINIO_ACCESS_KEY = "AKIAIOSFODNN7DOEMPLE"
        MINIO_SECRET_KEY = "wJalrXUtnFEMIK7MDENGbPxRfiCYDOEMPLEKEY"
        MINIO_PROMETHEUS_AUTH_TYPE = "public"
      }
    }
  }
}

job "metricbeat-scraper" {
  datacenters = ["dc1"]
  type = "service"

  group "collector" {
    task "metricbeat" {
      driver = "docker"

//      artifact {
//        source = "/home/<USER>/ebrosnan/metricbeat-config/metricbeat.prometheus.yml"
//        destination = "metricbeat"
//      }

      config {
        image = "https://docker.elastic.co/beats/metricbeat:7.9.2"
//        args = [
//          "metricbeat", "-e",
//          "-E", "output.elasticsearch.hosts=['dl0992obses001.nsw.education:9200']"
//        ]

//        volumes = [
//          "metricbeat/metricbeat.prometheus.yml:/usr/share/metricbeat/metricbeat.yml:ro",
//        ]
      }

      env {
        ELASTICSEARCH_HOSTS = "dl0992obses001.nsw.education:9200"
      }

    }
  }
}

import requests

CONSUL_ADDRESS  = "http://tl0992obscol01.nsw.education:8500"
CONSUL_TOKEN = 'a5515cc3-0ae7-ff5d-84f5-676b8088eabf'

# Configuration
#CONSUL_ADDRESS = "http://localhost:8500"  # Change this to your Consul server URL
deregister_endpoint = f"{CONSUL_ADDRESS}/v1/catalog/deregister"
list_nodes_endpoint = f"{CONSUL_ADDRESS}/v1/catalog/nodes"

def get_nodes():
    """Get a list of nodes from Consul"""
    response = requests.get(list_nodes_endpoint,headers={'X-Consul-Token': CONSUL_TOKEN})
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Failed to get nodes. Status code: {response.status_code}")
        return []

def deregister_node(node_id):
    """Deregister a node from Consul"""
    payload = {"Node": node_id}
    response = requests.put(deregister_endpoint,headers={'X-Consul-Token': CONSUL_TOKEN}, json=payload)
    if response.status_code == 200:
        print(f"Successfully deregistered node: {node_id}")
    else:
        print(f"Failed to deregister node: {node_id}. Status code: {response.status_code}")

def main():
    nodes = get_nodes()
    for node in nodes:
        deregister_node(node["Node"])

if __name__ == "__main__":
    main()
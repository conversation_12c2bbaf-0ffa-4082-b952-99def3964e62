job "metricbeat-prometheus-prod" {
  datacenters = ["dc-un-test"]
  type = "service"

  group "metricbeat" {

    task "metricbeat" {
      driver = "docker"

      config {
        image = "https://docker.elastic.co/beats/metricbeat:7.12.0"

        # always use dnsmasq to cache outgoing queries
        dns_servers = ["192.168.31.1"]

        port_map {
          prometheus_remote_write = 9201
        }

        volumes = [
          "local/prometheus.yml:/usr/share/metricbeat/modules.d/prometheus.yml",
          "local/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml"
        ]
      }

      resources {
        network {
          port "prometheus_remote_write" {
            static = 19410
          }
        }
      }

      service {
        name = "prometheus-remote-write-prod"
        port = "prometheus_remote_write"
      }

      env {
        ELASTICSEARCH_HOSTS = "pl0991obses04.nsw.education,pl0991obses05.nsw.education,pl0991obses06.nsw.education"
      }

      template {
        data = <<EOH
metricbeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

processors:
  - add_cloud_metadata: ~
  - add_docker_metadata: ~

output.elasticsearch:
  hosts: '${ELASTICSEARCH_HOSTS:elasticsearch:9200}'
  username: 'metricbeat'
  password: 'qtQBFDLSiDXPYoARrvxq'

setup.ilm.enabled: true

EOH
        destination = "local/metricbeat.yml"
      }

      template {
        data = <<EOH
- module: prometheus
  metricsets: ["remote_write"]
  host: "0.0.0.0"
  port: "9201"
EOH
        destination = "local/prometheus.yml"
      }
    }
  }
}


# Loki-rwb in PROD ObsCol cluster

# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  image_loki = "quay.education.nsw.gov.au/observability/loki:3.4.2"
  #jaeger_endpoint = "https://localhost/api/traces"
  loki_url = "https://loki.obs.nsw.education/loki/api/v1/push"
  env = "prod"
  }


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "loki-rwb" {
  datacenters = ["dc-cir-un-prod"]

  update {
    max_parallel      = 1
    health_check      = "checks"
    min_healthy_time  = "10s"
    healthy_deadline  = "3m"
    progress_deadline = "5m"
  }

#    constraint {
#      attribute = "${attr.unique.hostname}"
#      operator = "regexp"

#      value = "pl0475obscol.*"
#  }

  # Group Loki-Read  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-read" {
    count = 5 # @James - setting to 2 which allows for 1 of the 3 nodes to be down
    constraint { # @James - constraining so that tasks are not scheduled on the same node
      operator  = "distinct_hosts"
      value     = "true"
    }
    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
    #   canary = 1
    #   auto_promote = true
    #   auto_revert = true
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

#    volume "vol_loki" {
#      type      = "host"
#      source    = "vol_loki"
#      read_only = false
#    }    

    network {
      port "http" {
       # to = 3100
      }
      port "grpc" {
       # to = 9096
      }
    }

    task "loki-read" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki

        ports = [
          "http",
          "grpc",
        ]

        args = [
          "-target=read",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          "-log-config-reverse-order",
        ]

        volumes = [
          "/opt/localstore/loki/:/loki"
        ]

        logging {
          type = "loki"

          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=loki-read"
          }
        }

      }

      # volume_mount {
      #   volume      = "vol_loki"
      #   destination = "/loki"
      #   read_only   = false
      # }      

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        #JAEGER_ENDPOINT = var.jaeger_endpoint
      }
      
      service {
        name = "loki-read"
        port = "http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-read.entrypoints=http,https",
          "traefik.http.routers.loki-read.rule=Host(`loki.obs.nsw.education`)",
        ]

        check {
          name     = "Loki read"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"
          initial_status = "passing"
        }
      }

      resources {
        cpu    = 512
        memory = 2048
      }

    } // end-task loki-read
  } // end-group loki-read

  # Group Loki-Write  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-write" {
    count = 6 # @James - setting to 2 which allows for 1 of the 3 nodes to be down
    constraint { # @James - constraining so that tasks are not scheduled on the same node
      operator  = "distinct_hosts"
      value     = "true"
    }
    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
    #   canary = 1
    #   auto_promote = true
    #   auto_revert = true
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    # volume "vol_loki" {
    #   type      = "host"
    #   source    = "vol_loki"
    #   read_only = false
    # }    

    network {
      port "http" {
      }
      port "grpc" {
      }
    }

    task "loki-write" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki
        ports = [
          "http",
          "grpc",
        ]
        args = [
          "-target=write",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          "-log-config-reverse-order",
        ]
        volumes = [
        "/opt/localstore/loki/:/loki"
        ]     
        logging {
          type = "loki"
          config {
            loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=loki-write"
          }
        }

      }

      # volume_mount {
      #   volume      = "vol_loki"
      #   destination = "/loki"
      #   read_only   = false
      # }

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
        #JAEGER_ENDPOINT = var.jaeger_endpoint
      }
      service {
        name     = "Loki-write"
        port     = "http"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-write.entrypoints=https",
          "traefik.http.routers.loki-write-loki.rule=Host(`loki.obs.nsw.education`)&& Path(`/loki/api/v1/push`)",
        ]
        check {
          name     = "Loki write"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"
          initial_status = "passing"
        }
      }
      service {
        name     = "Loki-write-oltp"
        port     = "http"
   
        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-write-otlp.entrypoints=https",
          "traefik.http.routers.loki-write-oltp.rule=Host(`loki.obs.nsw.education`)&& Path(`/otlp/v1/logs`)",
        ]

        check {
          name     = "Loki write-otlp"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

      service {
        name     = "Loki-write-otel"
        port     = "http"
   
        
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-write-otel.entrypoints=https",
          "traefik.http.routers.loki-write-otel.rule=Host(`otel-loki.obs.nsw.education`)&& Path(`/loki/api/v1/push`)",
        ]

        check {
          name     = "Loki write-otel"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"
          initial_status = "passing"
        }
      }

      resources {
        cpu    = 512
        memory = 8000
      }

    }  // end-task  loki-write
  }  // end-group loki-write



  # Group Loki-Backend  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
  group "loki-backend" {
    count = 3

    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
    #   canary = 1
    #   auto_promote = true
    #   auto_revert = true
    }  

    ephemeral_disk {
      size   = 1000
      sticky = true
    }

    # volume "vol_loki" {
    #   type      = "host"
    #   source    = "vol_loki"
    #   read_only = false
    # }    

    network {
      port "http" {
        #to = 3100
      }
      port "grpc" {
        #to = 9096
      }
    }

    task "loki-backend" {
      driver = "docker"
      user   = "nobody"

      config {
        image = var.image_loki

        logging {
          type = "loki"

          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=loki-backend"
          }
        }

        ports = [
          "http",
          "grpc",
        ]

        args = [
          "-target=backend",
          "-config.file=/local/config.yml",
          "-config.expand-env=true",
          # @James - happy with adding this?
          # This dumps configuration, in reverse order (so it reads sensibly in grafana/loki log view) at startup.
          "-log-config-reverse-order",
        ]
        volumes = [
          "/opt/localstore/loki/:/loki"
      ]
      }

      # volume_mount {
      #   volume      = "vol_loki"
      #   destination = "/loki"
      #   read_only   = false
      # }      

      template {
        data        = file("assets/loki-rwb-config.yml")
        destination = "local/config.yml"
      }

      env {
        CONSUL_HTTP_ADDR = "http://localhost:8500"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = ".nsw.education,.det.nsw.edu.au,.consul,.svc,127.0.0.1,10.0.0.0/8,***********/16,**********/16"
      }
      
      service {
        name = "loki-backend"
        port = "http"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.loki-backend.entrypoints=https",
          "traefik.http.routers.loki-backend.rule=Host(`loki-backend.obs.nsw.education`)",
        ]

        check {
          name     = "Loki backend"
          port     = "http"
          type     = "http"
          path     = "/ready"
          interval = "20s"
          timeout  = "1s"

          initial_status = "passing"
        }
      }

      resources {
        cpu    = 512
        memory = 2048
      }

    }  // end-task loki-backend
  }  // end-group loki-backend  

}

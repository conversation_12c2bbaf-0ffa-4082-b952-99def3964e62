# LDAP Password Rotator

This Nomad job automates the rotation of LDAP service account passwords and updates the credentials in Vault.

## Overview

The job performs the following tasks:
1. Retrieves LDAP service account credentials from Vault
2. Authenticates against the LDAP endpoint
3. Generates a new secure password
4. Updates the LDAP password
5. Verifies the new password works
6. Stores the new password back in Vault

## Prerequisites

1. Nomad cluster
2. Vault server with the following:
   - A policy that allows reading and writing to the LDAP credential paths
   - LDAP credentials stored at the configured path

## Vault Configuration

### Required Vault Structure

The job expects the following structure in Vault:

```
secret/ldap/
├── config/
│   ├── host
│   ├── port
│   └── base_dn
└── service-account/
    ├── username
    └── password
```

### Setting Up Vault

1. Create the necessary paths in Vault:

```bash
# Store LDAP configuration
vault kv put secret/ldap/config \
  host=your-ldap-server.example.com \
  port=636 \
  base_dn="DC=example,DC=com"

# Store LDAP service account credentials
vault kv put secret/ldap/service-account \
  username="CN=service-account,OU=Service Accounts,DC=example,DC=com" \
  password="current-password"
```

2. Create a Vault policy for the job:

```hcl
# ldap-password-rotation.hcl
path "secret/ldap/config" {
  capabilities = ["read"]
}

path "secret/ldap/service-account" {
  capabilities = ["read", "update"]
}
```

Apply the policy:

```bash
vault policy write ldap-password-rotation ldap-password-rotation.hcl
```

## Deploying the Job

1. Customize the variables in the job file if needed:

```hcl
variable "datacenter" {
  default = "dc1"  # Change to your datacenter
}

variable "schedule" {
  default = "0 0 1 * *"  # Monthly on the 1st at midnight
}

variable "ldap_vault_path" {
  default = "secret/ldap"  # Path in Vault where LDAP credentials are stored
}
```

2. Deploy the job:

```bash
nomad job run ldap-password-rotator.nomad
```

## Monitoring and Troubleshooting

- Check job status:
  ```bash
  nomad job status ldap-password-rotator
  ```

- View logs:
  ```bash
  nomad alloc logs -job ldap-password-rotator
  ```

- Run the job manually:
  ```bash
  nomad job dispatch ldap-password-rotator
  ```

## Security Considerations

- The job requires access to Vault with permissions to read and update LDAP credentials
- The job generates a strong password with a mix of uppercase, lowercase, numbers, and special characters
- The script verifies the new password works before completing
- Consider implementing additional alerting if the password rotation fails

## Customization

You can customize the job by:
- Changing the schedule for password rotation
- Modifying the password generation logic
- Adding additional verification steps
- Implementing notifications on success/failure

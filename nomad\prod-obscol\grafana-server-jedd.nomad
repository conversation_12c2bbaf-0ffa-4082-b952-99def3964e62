// Grafana - jedd experimentation

// 2024-03 - preparation for:
//     a)  expiration of 'grafana.mtm' - migration of TEST grafana-obs instance
//     b)  expiration of on-prem enterprise licence (2024-04)



# ###############################################################################
# How to use:
#   If forking to an ephemeral grafana instance:
#       a)  change the job, group, and task names
#       b)  change the locals{} variables to uniquely identify your new instance.
# ###############################################################################

# Building / cloning your environment:
#
#  var/lib/grafana 
#     Copy /var/lib/grafana from upstream - and copy to the JOB's mapped 
#     ./grafana/var-lib-grafana/  -- then go into that directory and:
#          chown -R root:root
#          UPDATE _ do NOT do the following, as the plugin files need to be executable,
#          we I need to find an elegant way to keep executable, but change to g=x, w=x,
#          if o=x for files under plugins
#          find . -type f -exec chmod 664 {} \;
#          find . -type d -exec chmod 775 {} \;
#     Notes to self include:
#        o rm grafana.db (unused)
#        o rm licence.jwt.exp-*  (cruft)
#        o rm sessions directory structure (cruft, probably @TODO confirm)
#        o 'sqlite_db_for_portal' directory has 4 x 2021 vintage files AND a
#          file called 'portal_sla.db'  (sqlite) that's 22MB and accessed today.
#        o the ./csv/ directory can be removed  (@TODO confirm)
#        o the ./png/ directory can be removed  (@TODO confirm)
#        o the ./file-collections/ directory can be removed  (@TODO confirm)
#        o the BIG thing is obviously the ./plugins/ directory. 
#        o remove the licence jwt file
#        o remove the enterprise plugins and other troublesome plugins, including:
#             rm -rf grafana-splunk-datasource-136756-8e1d30d8/    (non-licenced)
#                    grafana-newrelic-datasource-136756-b3c1c1bb/  (non-licenced)
#                    grafana-jira-datasource*                      (non-licenced)
#                    grafana-image-renderer                        (errors at boot)
#                    btplc-status-dot-panel                        (errors at boot)
#                    grafana-influxdb-08-datasource                (errors at boot)
#
#  /etc/grafana/grafana.ini
#     We should make this ourselves from the assets / grafana ... init file
#     @TODO confirm we can ditch the JOB's mapped directory ./grafana/etc
#     ENSURE in assets/ template file that:
#        ==alerting==  is OFF
#        ==smtp / enabled== is FALSE
#        (otherwise you get a lot of emails coming out of the system)




# @TODO sort out https certs problems - might need copy of certs locally

# @TODO sso - almost definitely never achievable

# @TODO postgresql task - the grafana task should wait for this to be running, 
# otherwise we have at least one failed grafana allocation at each job-start as
# it gets ahead of postgresql readiness

# @TODO sanitise residual configuration (environment) variables below - put as 
# many into the assets/grafana-jedd.ini file as possible

# @TODO work out whether WARN about /usr/share/grafana/plugins-bundled should 
# be / can be suppressed or resolved - it's related to one of the PATHs we can 
# define, but probably should NOT be redefined from default (internal to container)
# as the variable defines bin/grafana and other key paths - but the docker container 
# does NOT ship with an empty plugins-bundled directory for some reason. Perhaps a 
# pre-start mkdir would suppress safely

# @TODO Fix backup - as of 2025-04-03 we get this error from the backup task:
#       pg_dump: error: connection to database "grafana" failed: connection to 
#       server at "*************", port 5432 failed: FATAL:  password authentication 
#       failed for user "grafana".
#       password retrieved from file "/var/lib/postgresql/.pgpass"
# While we're fixing (static) port 5432, it's not being bound to the 10. address of
# the host, so we can experiment with localhost:5432, or the 192.168.31.* address.





# Requirements = = = = = = = = = = = = = = = = = = = = = = = = = = = = =

# CONSUL variables must exist:
#     grafana-jedd/POSTGRES_DB
#     grafana-jedd/POSTGRES_USER
#     grafana-jedd/POSTGRES_PASSWORD




# Variables  = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
variables {
  # image_grafana = "quay.education.nsw.gov.au/observability/grafana-enterprise:test-obscol"
  # image_grafana = "quay.education.nsw.gov.au/observability/grafana-enterprise:10.1.5"

}

locals {
  # We are matching prod grafana during initial build as we will try to replicate
  # postgresql and other configuration data - maybe bringing across existing SSO
  # accounts - and can THEN upgrade in place to a more modern version of grafana.
  # Grafana prod mtm 2024-03-15 is ******* - but that's not available on docker hub
  # image_grafana = "grafana/grafana:10.1.10"
  image_grafana = "grafana/grafana:9.3.2"

  image_postgresql =  "postgres:12.17"

  # While testing, speed-up iterations by constraining to single host
  host_constraint = "pl.*obscol0[1]"
  # host_constraint = "pl.*obscol0[123]"

  loki_url = "https://loki.obs.nsw.education/loki/api/v1/push"

	# - - - - - - - - - - - - - - - - - - - - - -
	# - - - - - - - - - - - - - - - - - - - - - -
	# * These variables can be plumbed into the grafana-{...}.ini file, via the env section
	#      just ahead of the template / assets call.
	# * This means you MUST add an export THERE, if you add new elements HERE, and need to
	#      access these variables inside the template.
	# * The _intent_ of all this is to make it easier to spin up custom or ephemeral
	#      Grafana-server instances. BUT it's not well tested, so exercise some care.

	# This is used to uniquely identify components (postgresql-{$grafana_variant}) etc.
	# We don't want to use this to inform the other variables because that relationship
	# is not guaranteed - eg 'obs' (main, prod) will present as just grafana.obs.nsw.education,
	# but component tasks will have '-obs' suffix.
  grafana_variant = "jedd" 

	# Storage directory - this must be unique. Use 'grafana-server-' prefix to distinguish from other Grafana products.
  persistent_data = "/opt/sharednfs/grafana-server-jedd"

	# This is how the grafana application will self-identify.
	grafana_fqdn = "grafana-jedd.obs.nsw.education"

	# This won't change (unless you're replicating into test or dev cluster).
	grafana_domain = "obs.nsw.education"

	# Experimenting with this
  # postgresql_password = "${key "grafana-jedd/POSTGRES_PASSWORD"}"
  # postgresql_password = "${key "grafana-jedd/POSTGRES_PASSWORD"}"
  # postgresql_password = "${key "grafana-jedd/POSTGRES_PASSWORD"}"
  kv_postgresql_password = "grafana-jedd/POSTGRES_PASSWORD"



	# - - - - - - - - - - - - - - - - - - - - - -
	# - - - - - - - - - - - - - - - - - - - - - -
}


# Job  = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = = =
job "grafana-server-jedd" {
  datacenters = ["dc-cir-un-prod"]

  constraint {
    attribute = "${attr.unique.hostname}"
    operator = "regexp"
    value = local.host_constraint
  }

  type = "service"

  group "grafana-server-jedd" {

    network {
      port "port_grafana" {
        to = 3000
      }

      port "port_postgresql"  {
        # to = 5432
        static = 5432
      }
    }

    # Volumes are handled by explicit path to opt/sharednfs/ (refer below)

    # task postgresql  = = = = = = = = = = = = = = = = = = = = = = = = =
    task "postgresql" {
      driver = "docker"

      kill_signal = "SIGTERM"      
      kill_timeout = "90s"
      # user = "postgres:postgres"

      # Volumes are handled by explicit path to opt/sharednfs/ (refer below)

      config {
        image = "${local.image_postgresql}"

        # userns does not work - but privileged does - to solve the 'cannot change permission on files' error
        # userns_mode = "host"
        privileged = true

        ports = ["port_postgresql"]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        volumes = [
          "${local.persistent_data}:/persistent",
          "${local.persistent_data}/postgresql/data:/var/lib/postgresql/data",
          "${local.persistent_data}/postgresql/data:/var/lib/pgsql/12/data"
        ]

      }

      env = {
###
### All this has been relocated to the assets/grafana-server-jedd file - wherein we
### configure password (via a call to the consul key/value store elsewhere in _this_ file)
### and then define all the non-sensitive stuff under the [database] section of that file.
###
#        "POSTGRES_DB"       = "grafana",
#        "POSTGRES_USER"     = "grafana",
#        "POSTGRES_PASSWORD" = "S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU",
#        # "PGPORT"            = "${NOMAD_PORT_port_postgresql}",
#        "PGPORT"            = "5432",
#        "PGDATA"            = "/persistent/postgresql/data",
#
#        # This SHOULD do something functionally equivalent to:
#        #   echo "host all all all $POSTGRES_HOST_AUTH_METHOD" >> pg_hba.conf
#        # Which SHOULD resolve our 'no pg_hba.conf entry for ************' error - but does not
#        "POSTGRES_HOST_AUTH_METHOD" = "md5"
#
        # We should be able to brute force this with 'trust' but that doesn't work either
        # "POSTGRES_HOST_AUTH_METHOD" = "trust"
      }

      resources {
        cpu    = 2500
        memory = 5000
      }

      service {
        name = "postgresql"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana-postgresql.entrypoints=http",
          "traefik.http.routers.grafana-postgresql.rule=Host(`grafana-postgresql.obs.nsw.education`)",
        ]      

        port = "port_postgresql"

##        check {
##          name     = "PostgreSQL for Grafana healthcheck"
##          port     = "port_postgresql"
##          type     = "http"
##          path     = "/ready"
##          interval = "20s"
##          timeout  = "5s"
##          check_restart {
##            limit           = 3
##            grace           = "60s"
##            ignore_warnings = false
##          }
##        }

      }
    }    #  end-task  postgresql


    # task grafana  = = = = = = = = = = = = = = = = = = = = = = = = = =
    task "grafana" {
      driver = "docker"

      kill_signal = "SIGTERM"      

      # This sidecar approach should force Grafana to 'post start' after PostgreSQL - though
      # in practice Grafana should start fine without PostgreSQL answering immediately, and
      # just retry until the DB is ready.
      #lifecycle {
      #  hook = "poststart"
      #  sidecar = true
      #}

      # user = "root:root"

      config {
        image = "${local.image_grafana}"

        privileged = true

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

        ports = ["port_grafana"]

        volumes = [

          # 2023-10-23 jedd - using THIS instead of relying on the GF_DATA ... paths, as those really
          #   don't seem to be respected.  This approach maintains plugins and other data between restarts.
          "${local.persistent_data}:/persistent",
          "${local.persistent_data}/grafana/var-lib-grafana:/var/lib/grafana",

          "local/grafana.ini:/etc/grafana/grafana.ini",
          "local/ldap.toml:/etc/grafana/ldap.toml",
          # @TODO copy prod license.jwt to /opt/sharednfs/grafana-jedd/grafana/license.jwt
          # "local/license.jwt:/persistent/grafana/license.jwt",
          "local/EXT_PROD_CHAIN.crt:/etc/pki/CA/certs/EXT_PROD_CHAIN.crt",
        ]

      }

#==      env {
#==        # Expose Grafana on this port - as with other ObsObs jobs we're sticking with standard ports for each job.
#==        GF_SERVER_HTTP_PORT   = "${NOMAD_PORT_port_grafana}"
#==
#==        # This points to /grafana is our persistent mount point - this directory defaults to /var/lib/grafana, 
#==        # and would contain several sub-directories: alerting/, csv/, plugins/, and png/
#==        # as well as the default location for the sqlite database file:   grafana.db
#==        # Note - plugins/ does NOT get created here at instantiation - but is at /var/lib/grafana/plugins internally - needs more experimentation
#==
#==        # 2023-10-23 jedd - removing this and forcing the volume overlay (above) directly to /var/lib/grafana
#==        # GF_PATHS_DATA         = "/persistent/grafana/DATA"
#==
#==        # Usually points to /usr/share/grafana - contains plugins-bundled and other distro / shipped files - we may want more control over this.
#==        # DISABLING - as this is about 300MB on grafana.mtm and contains lots of things we actually don't want to replicate .. probably
#==        # GF_PATHS_HOME         = "/persistent/grafana/usr-share"
#==
#==        # This defaults to /var/log/grafana - we may have better ways of extracting logs via nomad (docker/loki) but
#==        # for troubleshooting it should be convenient. Frustratingly Grafana employees like to pluralise everythings.
#==        GF_PATHS_LOGS         = "/grafana/log"
#==
#==        # GF_LOG_LEVEL “debug”, “info”, “warn”, “error”, and “critical”. Default is info
#==        # GF_LOG_LEVEL = "warn"
#==        GF_LOG_LEVEL = "debug"
#==
#==        # We can send logs to console (captured by loki above), or file (dumped to GF_PATHS_LOGS above), or both with "console file"
#==        GF_LOG_MODE           = "console"
#==
#==        # We probably want this set so we can call in dashboards from grafana.com
#==        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
#==        HTTP_PROXY  = "http://proxy.det.nsw.edu.au:80"
#==        NO_PROXY    = "10.0.0.0/8,172.0.0.0/8,192.168/16.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.consul"
#==
#==        # Pass the plugins you want installed to Docker with the GF_INSTALL_PLUGINS environment variable as a 
#==        # comma-separated list. This sends each plugin name to grafana-cli plugins install ${plugin} and installs 
#==        # them when Grafana starts.
#==        # GF_INSTALL_PLUGINS = "redis-datasource"
#==
#==        # We can load this up via the template (below) to autogenerate two datasources, though we prefer to keep
#==        # those in the SQLite database in GF_PATHS_DATA (/opt/grafana in host) so we can modify them, and more
#==        # conveniently add new items.  Commented out (also the template below) and create from within the UI.
#==        # GF_PATHS_PROVISIONING = "/local/grafana/provisioning"
#==
#==        # I can't even find a reference to this on the googles in 2022-11
#==        # GF_LIVE_ALLOWED_ORIGINS = "http://*"
#==
#==        # This is another poorly documented feature, and I'm doubtful that we need it.
#==        # GF_FEATURE_TOGGLES_ENABLE = "ngalert"
#==
#==        # Database configuration - should probably not be in grafana.ini as this way we can utilise
#==        # the same credentials and secrets from Consul that are then used by the postgresql-backup task below.
#==        GF_DATABASE_TYPE = "postgres"
#==
#==        # original grafana.mtm credentials
#==        #GF_DATABASE_NAME = "grafana_db_admin"
#==        #GF_DATABASE_USER = "grafanadb_p"
#==        #GR_DATABASE_PASSWORD = S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU
#==        GF_DATABASE_NAME = "grafana"
#==        GF_DATABASE_USER = "grafana"
#==        GF_DATABASE_PASSWORD = "S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU"
#==
#==
#==        #GF_DATABASE_HOST = "${NOMAD_ADDR_port_postgresql}"
#==        # GF_DATABASE_NAME = {{ key "grafana-jedd/POSTGRES_DB" }}
#==        # GF_DATABASE_USER = {{ key "grafana-jedd/POSTGRES_USER" }}
#==        # GF_DATABASE_PASSWORD = {{ key "grafana-jedd/POSTGRES_PASSWORD" }}
#==
#==        #GF_DATABASE_NAME = "grafana"
#==        #GF_DATABASE_USER = "grafana"
#==        #GF_DATABASE_PASSWORD = "password"
#==
#==        # With Postgresql can be: "require" (default), "verify-full", "verify-ca", and "disable"
#==        #GF_DATABASE_SSL_MODE = "disable"
#==
#==        # GF_DATABASE_LOG_QUERIES = "true"
#==
#==        # GF_SERVER_ROOT_URL = "http://grafana.obs.nsw.education"
#==
#==        GF_ALERTING_ENABLED = "false"
#==        GF_UNIFIED_ALERTING_ENABLED = "false"
#==
#==        GF_DEFAULT_FORCE_MIGRATION  = "false"
#==      }
#==



      service {
        name = "grafana"
        port = "port_grafana"

#        check {
#          type = "http"
#          port = "port_grafana"
#          path = "/api/health"
#          interval = "5s"
#          timeout = "2s"
#        }

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.grafana.entrypoints=http,https",
          "traefik.http.routers.grafana.tls=false",
          # "traefik.http.routers.grafana.rule=Host(`grafana-jedd.obs.nsw.education`)",
          "traefik.http.routers.grafana.rule=Host(`${local.grafana_fqdn}`)",
        ]      
      }

      resources {
        cpu    = 700
        memory = 3000
      }

#### Disabling this entirely as we will create the data sources and persist them in the /grafana
#### storage mapped above onto host's /opt/grafana structure.  

# @TODO explore failure states when provisioning extant datasource, and/or provision fails (remote
# end not available) for single source, and/or editing of an existing datasource originally auto-
# provisioned, etc.  Persistence is where we are coming from, and may better suit operational and
# upgrade processes.

      template {
        # This is PURELY to get the  postgresql password out of an abstracted
        # locals.variable (see above) and then into a variable we can push into
        # the template load of the assets file, and retrieve from there as:
        #      $__env{POSTRESQL_PASSWORD}  
        data = <<EOH
POSTGRESQL_PASSWORD="{{ key "${local.kv_postgresql_password}" }}"

EOH
        destination = "local/env"
        env = true
      }


      env  {
        # - - - - - - - - - - - - - - - - - - - - - -
        # Refer notes in the locals{} stanza at the head of this file - these env translations
        # let us refer to the variables in the template assets/file as it's loaded up.
        # - - - - - - - - - - - - - - - - - - - - - -
        PERSISTENT_DATA = local.persistent_data
        GRAFANA_FQDN = local.grafana_fqdn
        GRAFANA_DOMAIN = local.grafana_domain
        POSTGRESQL_PASSWORD = "${POSTGRESQL_PASSWORD}"
#        POSTGRESQL_PASSWORDa = env.POSTGRESQL_PASSWORDa
#        POSTGRESQL_PASSWORDb = env.POSTGRESQL_PASSWORDb
#        J = "${PGQ}"
        #PG="${PG}"
        #PGQ="${PGQ}"

#        POSTGRESQL_PASSWORD = "{{ key "grafana-jedd/POSTGRES_PASSWORD" }}"

      }

      template {
        # We have a modified version of the original grafana.mtm. (pu0992tagf0001)
        # grafana.ini that we're starting with.  
        # 2025-05 jedd - changing to template (tpl) file so we can use nomadvars inside it
        data = file("./assets/grafana-server-jedd.ini")
        env = false
        destination = "local/grafana.ini"
      }

      template {
        # Not 100% sure this is actually used - no documentation was made of the original
        # grafana configuration / build.
        data = file("./assets/grafana-jedd.ldap.toml")
        destination = "local/ldap.toml"
      }

      template {
        data = file("./assets/grafana-jedd.ext_prod_chain.crt")
        destination = "local/EXT_PROD_CHAIN.crt"
      }

#      template {
#        # Licence license file is keyed to fqdn hostname and uses american spelling of the noun.
#        # Because DATA is pointing to persistent/grafana/DATA - we can keep license key on sharednfs, probably
#        data = file("./assets/grafana-jedd.license.jwt")
#        destination = "local/license.jwt"
#      }

    } // end-task grafana


    # task postgresql-backup = = = = = = = = = = = = = = = = = = = = = = = = =
    task "postgresql-backup" {
      driver = "docker"

      kill_signal = "SIGTERM"      
      user = "postgres:postgres"

      config {
        image = "${local.image_postgresql}"

        command = "/backup-looper.sh"

        volumes = [
          "${local.persistent_data}:/persistent",
          "local/backup-looper.sh:/backup-looper.sh",
        ]

        logging {
          type = "loki"
          config {
            loki-url = local.loki_url
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }

      }

      env = {
        # It's just easier if we have local timezone inside the container.
        "TZ" = "Australia/Sydney",
      }

      resources {
        cpu    = 512
        memory = 1024
      }

      service {
        name = "postgresql-backup"
      }

      #  FILE:   backup-looper.sh
      #  This is our over-ridden entry point - we're just here for pg_dump but use the same 
      #  postgresql *server* image as we've already got it in cache on this host AND we get
      #  guaranteed client / server version alignment for free.
      template {
        data = <<EOH
#! /usr/bin/env bash

# Heavily opinionated backup script for small PostgreSQL database
#
# Sleep regularly, wake up to detect if we're in one of the right windows for the
# day (typically once a day, but can be adjusted below).  If so, perform a db dump
# then return to sleep.

TARGETDIR=/persistent/BACKUPS

if [ !  -d ${TARGETDIR} ]
then
  mkdir -p ${TARGETDIR}
fi

# Feeding a password to pg_dump is easier if we just use the ~/.pgpass convention
# in format:  hostname:port:database:username:password



# @TODO extract this back to consul key / value OR vault OR at least nomad vars
# echo {{ env "NOMAD_ADDR_port_postgresql" }}:{{ key "grafana-jedd/POSTGRES_DB" }}:{{ key "grafana-jedd/POSTGRES_USER" }}:{{ key "grafana-jedd/POSTGRES_PASSWORD" }} > ~/.pgpass
# echo {{ env "NOMAD_ADDR_port_postgresql" }}:grafana:grafana:S8yPqVDQp5GzZdZSy6ee53fZfbFUaRpU {{ key "grafana-jedd/POSTGRES_PASSWORD" }} > ~/.pgpass
# echo {{ env "NOMAD_ADDR_port_postgresql" }}:grafana:grafana:{{ key "${local.kv_postgresql_password}" }} {{ key "grafana-jedd/POSTGRES_PASSWORD" }} > ~/.pgpass
echo {{ env "NOMAD_ADDR_port_postgresql" }}:grafana:grafana:{{ key "${local.kv_postgresql_password}" }} > ~/.pgpass

# Must be set to limited rights or else it ignores the file.
chmod 600 ~/.pgpass

while [ 1 ]
do
  # Sleep first, as the database is typically not ready on instantiation anyway
  sleep 1h

  HOUR=`date "+%H"`
  TARGETFILE=grafana-jedd-postgresql-dump-`date "+%a-%H"`H.sql

  # Multi-value alternative:
  # if [ ${HOUR} -eq 08 ] || [ ${HOUR} -eq 16 ] || [ ${HOUR} -eq 23 ] 

  # Daily option:
  if [ ${HOUR} -eq 23 ]
  then
    # First - remove the 1-week old archive
    rm ${TARGETDIR}/${TARGETFILE}.gz

    # pg_dump requires the following params despite them being in pgpass - pgpass is a pattern
    # matching file only, and password is retrieved when user/db/addr matches.
    pg_dump -f ${TARGETDIR}/${TARGETFILE}                    \
            -Fc                                              \
            -d {{ key "grafana-jedd/POSTGRES_DB" }}           \
            -U {{ key "grafana-jedd/POSTGRES_USER" }}         \
            -h {{ env "NOMAD_HOST_IP_port_postgresql" }}     \
            -p {{ env "NOMAD_HOST_PORT_port_postgresql" }}       

    # The -Fc format is recommended - ostensibly it is compressed but in practice not optimally,
    # so we compress properly with gzip as the final step.
    gzip --best ${TARGETDIR}/${TARGETFILE}
  fi
done

EOH
        destination = "local/backup-looper.sh"
        perms = "755"
      }
    }    #  end-task "postgresql-backup"

  }
}

// Grafana <PERSON> (prod obscol)

variables {
    image_tempo = "quay.education.nsw.gov.au/observability/tempo:prod-obscol"
  }

job "tempo" {
  datacenters = ["dc-cir-un-prod"]
  type = "service"
  
  update {
    max_parallel = 2 #allows for there to be at least two instance running avoiding data dropouts
    health_check = "checks" #specifies the allocation should be considered healthy
    auto_revert = true
    healthy_deadline = "5m"
    min_healthy_time = "5s"
  }

  group "tempo" {
    count = 1
    
    constraint {
        attribute = "${attr.unique.hostname}"
        operator = "regexp"
        value = "pl0992obscol0[123]"
    }

    network {
      port "port_tempo" {
        to = 3200
      }
      port "port_zipkin" {
        static = 9411
        to = 9411
      }
      port "port_jaeger" {
        to = 14268
      }
      port "port_otlphttp" {
        to = 4318
      }      
    }

    volume "vol_tempo"  {
      type = "host"
      source = "vol_tempo"
      read_only = false
    }
    service {
      name = "tempo"
      port = "port_tempo"

      check {
        type     = "tcp"
        interval = "10s"
        timeout  = "2s"
      }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.tempo.rule=Host(`tempo.obs.nsw.education`)",
          "traefik.http.routers.tempo.tls=false",
          "traefik.http.routers.tempo.entrypoints=http,https",
        ]      
    }

    service {
      name = "otlp-traces"
      port = "port_otlphttp"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.otlp-traces.rule=Host(`traces.obs.nsw.education`) && Path(`/v1/traces`)",
          "traefik.http.routers.otlp-traces.tls=false",
          "traefik.http.routers.otlp-traces.entrypoints=http,https",
        ]      
    }

    service {
      name = "jaeger"
      port = "port_jaeger"
      check {
        type     = "tcp"
        interval = "10s"
        timeout  = "2s"
      }
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.jaeger.rule=Host(`jaeger.obs.nsw.education`)",
          "traefik.http.routers.jaeger.tls=false",
          "traefik.http.routers.jaeger.entrypoints=http,https",
        ]      
    }
    task "tempo" {
      driver = "docker"
      kill_signal = "SIGTERM"      

      env {
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        #JAEGER_ENDPOINT = "https:///api/traces?format=jaeger.thrift"     
      }

      config {
        image = var.image_tempo
        logging {
          type = "loki"
          config {
            loki-url = "https://otel-loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=tempo-all"
          }
        } 

        args = [
          "--config.file=/etc/tempo/config/tempo.yml",
        ]

        ports = [
          "port_tempo",
          "port_otlphttp",
          "port_jaeger"
        ]

        volumes = [
          "local/config:/etc/tempo/config",
        ]
        
      }

      volume_mount {
        volume = "vol_tempo"
        destination = "/mnt/tempo"
        read_only = false
      }

      template {
        data        = file("assets/tempo-config.yml")
        destination = "local/config/tempo.yml"
      }
      resources {
        cpu    = 512
        memory = 4096
        memory_max = 12000
      }

    }
  }
}

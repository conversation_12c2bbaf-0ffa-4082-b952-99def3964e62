## Stolen example from https://github.com/leowmjw/nomad-box

job "node-red" {

  datacenters = ["dc-un-test"]

  type = "service"

  update {
    stagger = "10s"
    max_parallel = 1
  }

  group "control" {

    network {
      port "port_nodered" {
        static = 1880
      }
    }

    count = 1

    restart {
      attempts = 10
      interval = "5m"
      delay = "25s"
      mode = "delay"
    }

    constraint {
      attribute = "${attr.unique.hostname}"
      value = "tu0992tcdnd002.hbm.det.nsw.edu.au"
    }

    ephemeral_disk {
      size = 300
    }

    task "node-red" {
      driver = "docker"

      env = {
        "PROMETHEUS_COLLECT_DEFAULT_METRICS" = "true",
        "NODE_RED_ENABLE_PROJECTS" = "true"
        HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }

      config {
        image = "nodered/node-red:latest"
        ports = ["port_nodered"]
        volumes = ["local:/data"]
        }

      resources {
        cpu    = 500
        memory = 256
      }

      service {
        name = "nodered-app"
        tags = [
          "traefik.enable=true",
          "traefik.http.routers.nodered.rule=Host(`nodered.obs.test.nsw.education`)",
          "traefik.http.routers.nodered.tls=false",
          "traefik.http.routers.nodered.entrypoints=http,nodered",
        ]
        port = "port_nodered"
        check {
          name     = "alive"
          type     = "tcp"
          interval = "10s"
          timeout  = "2s"
        }
      }
    }
  }
}

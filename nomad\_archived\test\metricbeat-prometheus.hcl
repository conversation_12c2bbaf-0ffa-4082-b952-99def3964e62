job "metricbeat-prometheus" {
  datacenters = ["dc-un-test"]
  type = "service"

  group "metricbeat" {

    task "metricbeat" {
      driver = "docker"

      config {
        image = "https://docker.elastic.co/beats/metricbeat:7.12.0"

        # always use dnsmasq to cache outgoing queries
        dns_servers = ["192.168.31.1"]

        port_map {
          prometheus_remote_write = 9201
        }

        volumes = [
          "local/prometheus.yml:/usr/share/metricbeat/modules.d/prometheus.yml",
          "local/metricbeat.yml:/usr/share/metricbeat/metricbeat.yml"
        ]
      }

      resources {
        network {
          port "prometheus_remote_write" {
            static = 19409
          }
        }
      }

      service {
        name = "prometheus-remote-write"
        port = "prometheus_remote_write"
      }

      env {
        ELASTICSEARCH_HOSTS = "http://tl0992obses01.nsw.education:9200,http://tl0992obses02.nsw.education:9200,http://tl0992obses03.nsw.education:9200"
      }

      template {
        data = <<EOH
metricbeat.config.modules:
  path: ${path.config}/modules.d/*.yml
  reload.enabled: false

processors:
  - add_cloud_metadata: ~
  - add_docker_metadata: ~

output.elasticsearch:
  hosts: '${ELASTICSEARCH_HOSTS:elasticsearch:9200}'

setup.ilm.enabled: true

EOH
        destination = "local/metricbeat.yml"
      }

      template {
        data = <<EOH
- module: prometheus
  metricsets: ["remote_write"]
  host: "0.0.0.0"
  port: "9201"
EOH
        destination = "local/prometheus.yml"
      }
    }
  }
}

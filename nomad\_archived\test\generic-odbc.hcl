job "generic-odbc" {
  datacenters = ["dc-un-test"]
  type = "service"

  group "collector" {
    count = 1

    # Temporary constraint allows us to move to nomad while preserving DNS CNAMES at first
    constraint {
      attribute = "${attr.unique.hostname}"
      value = "tu0992tcdnd001.hbm.det.nsw.edu.au"
    }


    task "zabbix-proxy" {
      driver = "docker"

      artifact {
        source = "git::ssh://****************************:7999/mbd/zabbix-odbc.git"
        destination = "odbc"

        options {
          sshkey = "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        }
      }

      config {
        image = "https://artifacts.mtm.nsw.education/zabbix-proxy-doe:centos-5.0.3-odbc"
        hostname = "collector-generic-odbc.mtm.test.det.nsw.edu.au"

        port_map {
          zabbix_passive = 10050
          zabbix_server = 10051
        }

        volumes = [
          "odbc/odbc.ini:/etc/odbc.ini",
        ]
      }

      service {
        name = "generic-odbc-passive"
        port = "zabbix_passive"


        check {
          type = "tcp"
          port = "zabbix_passive"
          interval = "20s"
          timeout = "10s"
        }
      }

      service {
        name = "generic-odbc"
        port = "zabbix_server"


        check {
          type = "tcp"
          port = "zabbix_server"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        ZBX_HOSTNAME = "collector-generic-odbc.mtm.test.det.nsw.edu.au"
        ZBX_SERVER_HOST = "tu0992tedbd001.hbm.det.nsw.edu.au"
        DB_SERVER_HOST = "${NOMAD_IP_db_postgresql}"
        DB_SERVER_PORT = "${NOMAD_PORT_db_postgresql}"
        ZBX_PROXYOFFLINEBUFFER = "3"
        ZBX_STARTPOLLERS = "32"
        ZBX_STARTPOLLERSUNREACHABLE = "16"
        ZBX_STARTTRAPPERS = "15"
        ZBX_STARTPINGERS = "16"
        ZBX_STARTDISCOVERERS = "8"
        ZBX_CACHESIZE = "256M"
        ZBX_HISTORYCACHESIZE = "128M"
        ZBX_HISTORYINDEXCACHESIZE = "32M"
        ZBX_UNREACHABLEPERIOD = "120"
        ZBX_UNREACHABLEDELAY = "30"
      }

      resources {
        cpu = 400
        memory = 1000

        network {
          port "zabbix_passive" {}
          port "zabbix_server" {}
        }
      }
    }

    task "db" {
      driver = "docker"

      config {
        image = "https://artifacts.mtm.nsw.education/postgres:12-novolume"

        port_map {
          postgresql = 5432
        }
      }

      service {
        name = "generic-odbc-db"
        port = "postgresql"

        check {
          type = "tcp"
          port = "postgresql"
          interval = "20s"
          timeout = "10s"
        }
      }

      env {
        POSTGRES_DB = "zabbix_proxy"
        POSTGRES_USER = "zabbix"
        POSTGRES_PASSWORD = "zabbix"
      }

     resources {
       cpu = 400
       memory = 1500

        network {
          port "postgresql" {}
        }
      }
    }
  }
}

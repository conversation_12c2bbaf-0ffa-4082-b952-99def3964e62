job "proxy" {
  datacenters = ["dev"]
  type = "system"

  update {
    stagger = "5s"
    max_parallel = 1
  }

  group "envoy" {
    count = 1
    task "envoy" {
      driver = "docker"
      config {
        image = "envoyproxy/envoy:v1.10.0"
        command = "/usr/local/bin/envoy"
        args = [
          "--concurrency 4",
          "--config-path /local/envoy-zabbix.yaml",
          "--mode serve",
        ]
        network_mode = "host"
      }
      template {
        source        = "envoy-zabbix.yaml"
        destination   = "local/envoy-zabbix.yaml"
        change_mode   = "restart"
      }
      resources {
        network {
          mbits = 10
          port "envoy" {
            static = 1010
          }
        }
      }
    }
  }
}
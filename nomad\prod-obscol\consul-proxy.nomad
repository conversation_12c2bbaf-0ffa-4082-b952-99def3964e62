
// consul-proxy -- front end to access consul on localhost 8500 via 443 (traefik)


variables {
  image_caddy = "quay.education.nsw.gov.au/observability/caddy:2.7"
}



# JOB - consul-proxy  = = = = = = = = = = = = = = = = = = = = = = = = =
job "consul-proxy" {
  datacenters = ["dc-cir-un-prod"]

  type = "service"

  group "proxy" {
    network {
      port "http" {
        to = 8080
      }
      port "metrics" {
        to = 2019
      }
    }

    constraint {
      # This really only is useful if running on a server, not client/agent.
      attribute = "${attr.unique.hostname}"
      operator = "regexp"
      value = "pl.*obscol0[123]"
    }

    service {
      name = "consul-proxy"
      port = "http"
      
      tags = [
        "traefik.enable=true",
        "traefik.http.routers.consul.rule=Host(`consul.obs.nsw.education`)",
        "traefik.http.routers.consul.tls=true"
				# "traefik.http.routers.home.entrypoints=http,https",
      ]

      # Additional service registration for Prometheus scraping
    }

		service {
			name = "consul-proxy-metrics"
			port = "metrics"
			tags = ["metrics"]
		}

    task "caddy" {
      driver = "docker"

      config {
        image = "${var.image_caddy}"

        ports = ["http", "metrics"]

        mount {
          type = "bind"
          target = "/etc/caddy/Caddyfile"
          source = "local/Caddyfile"
          readonly = true
        }
      }

      template {
        data = <<EOF
{
  auto_https off
#  servers {
#    metrics
#  }
  admin :2019
}

:8080 {
  reverse_proxy {{ env "NOMAD_HOST_IP_http" }}:8500 {
    header_up X-Forwarded-Proto "https"
#    header_up X-Forwarded-For {remote_host}
    header_up Host {host}
    header_up Authorization {header.Authorization}
  }

  # Enhanced logging with timing information
  log {
    output stdout
    format console {
      time_format "rfc3339"
      level_format "color"
    }
    level INFO
  }

  # Enable Prometheus metrics
#  metrics {
#    path /metrics
#    disable_openmetrics
#  }
}
EOF
        destination = "local/Caddyfile"
      }

      resources {
        cpu    = 200
        memory = 256
      }
    }
  }
}










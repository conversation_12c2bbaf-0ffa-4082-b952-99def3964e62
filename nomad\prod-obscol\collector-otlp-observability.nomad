# Copyright (c) HashiCorp, Inc.
# SPDX-License-Identifier: MPL-2.0

# Nomad adaption of the Kubernetes example from
# https://github.com/open-telemetry/opentelemetry-collector/blob/main/examples/k8s/otel-config.yaml

variables {
  otel_image = "quay.education.nsw.gov.au/observability/otel-contrib:prod-obscol"
  #otel_image = "otel/opentelemetry-collector-contrib:latest"
}

job "collector-otlp-observability" {
  datacenters = ["dc-cir-un-prod"]
  type        = "service"
  
  update {
    max_parallel      = 1
    min_healthy_time  = "10s"
    healthy_deadline  = "2m"
    canary            = 1
    auto_promote      = true
    auto_revert       = true
  }

  group "otlp-observability" {
    count = 1
    update {
      max_parallel     = 1
      min_healthy_time = "10s"
      healthy_deadline = "2m"
      canary = 1
      auto_promote = true
      auto_revert = true
    }        

    network {

      port "healthcheck" {
      }
    #   port "jaeger-grpc" {
    #     to = 14250
    #   }
    #   port "jaeger-thrift-http" {
    #     to = 14268
    #   }
      port "metrics" {
      }
      port "otlp" {
        to = 4317
      }
      port "loki-receiver" {
        to = 3500
      }      
      port "otlphttp" {
        to = 4318
      }
    #   port "zipkin" {
    #     to = 9411
    #   }
      port "zpages" {
        to = 55679
      }
      port "pprof" {
        to = 1777
      }
    }

    service {
      name     = "otel-collector-metrics"
      port     = "metrics"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-observability.entrypoints=https",
        "traefik.http.routers.otel-app-observability.rule=Host(`otel-app-observability.obs.nsw.education`) && Path(`/metrics`)",
        "traefik.http.routers.otel-app-observability.tls=false"
      ]
    }


    service {
      name     = "otel-app-observability-pprof"
      port     = "pprof"
      provider = "consul"

      tags = [
        "traefik.enable=true",
        "traefik.http.routers.otel-app-observability-pprof.entrypoints=https",
        "traefik.http.routers.otel-app-observability-pprof.rule=Host(`otel-app-observability-pprof.obs.nsw.education`) && Path(`/debug/pprof`)",
        "traefik.http.routers.otel-app-observability-pprof.tls=false"
      ]
    }

      service {
        name =  "otel-app-observability-otlp"
        port = "otlphttp"
        provider = "consul"
        tags = [
          "traefik.http.routers.otel-collector-http.rule=Host(`otel-collector-http.obs.nsw.education`)",
          "traefik.http.routers.otel-collector-http.entrypoints=http,https",
          "traefik.http.routers.otel-collector-http.tls=false",
          "traefik.enable=true",
        ]
      }

    task "otel-collector" {
      driver = "docker"
      env = {
        #HTTP_PROXY = "http://proxy.det.nsw.edu.au:80"
        #HTTPS_PROXY = "http://proxy.det.nsw.edu.au:80"
        #NO_PROXY = "10.0.0.0/8,*********/8,.det.nsw.edu.au,.nsw.education,.int.education.nsw.gov.au,.collectors.obs.nsw.education,.consul"
      }
      config {
        image = var.otel_image
        force_pull = false

        entrypoint = [
          "/otelcol-contrib",
          "--config=local/otel/config.yaml",
        ]
        logging {
            type = "loki"
            config {
                loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
                loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod,service_name=otel-collector-observability"                
                }
            }
        ports = [
        "otlphttp",
        # "zipkin",
        "zpages",
        "pprof",
        "healthcheck",
        # "jaeger-grpc",
        # "jaeger-thrift-http",
        "metrics",
        "otlp",
        "loki-receiver"
        ]
      }

      resources {
        cpu    = 1000
        memory = 2048
      }

      template {
        data = file("assets/collector-opentelemetry-observability.yaml")
        destination   = "local/otel/config.yaml"
      }
    }
  }
}

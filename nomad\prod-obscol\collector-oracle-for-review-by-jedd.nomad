
# collector-oracle - PROD obscol - prometheus + exporter


// docker commandline is:
//   docker run   -p 9161:9161 -e DATA_SOURCE_NAME="system/oracle@//**************:1521" iamseth/oracledb_exporter

// minimalistic docker hub page:
//   https://hub.docker.com/r/iamseth/oracledb_exporter

// github project page:
//   https://github.com/iamseth/oracledb_exporter

// Multi-target support should be present, from github page above:

// "To use the multi-target functionality, send a http request 
//  to the endpoint /scrape?target=foo:1521 where target is set
//  to the DSN of the Oracle instance to scrape metrics from."


variables {
  image_prometheus = "prom/prometheus:v2.46.0"
  # As of 2023-09, docker.hub latest is 2yo (2021-03-17)
  image_exporter = "iamseth/oracledb_exporter:latest"
}


job "collector-oracle" {

  type = "service"

  datacenters = ["dc-cir-un-prod"]


  # GROUP collector  = = = = = = = = = = = = = = = = = = = = = = = = =
  group "collector-oracle" {
    
    restart {
      interval = "10m"
      attempts = 20
      delay    = "30s"
    }

    network {
      port "port_prometheus" { }

      port "port_exporter_blank" { 
        to = 9161
      }

      port "port_exporter_mamid" { 
        to = 9161
      }
  	}
    

    # TASK prometheus = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-oracle-prometheus" {
      driver = "docker"

      config {
        image = var.image_prometheus

        args = [
          "--config.file=/etc/prometheus/prometheus.yml",
          "--enable-feature=agent",
          "--web.external-url=http://prometheus-app-oracle.obs.nsw.education",
          "--web.listen-address=0.0.0.0:${NOMAD_PORT_port_prometheus}",
          "--web.page-title=Prometheus for Oracle Services",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }          
        volumes = [
          "local/prometheus.yaml:/etc/prometheus/prometheus.yml",
        ]
        ports = [ "port_prometheus" ]
      }

      resources {
        cpu = 50
        memory = 150
        memory_max = 250
      }

      service {
        name = "collector-oracle-prometheus"
        port = "port_prometheus"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oracle-prometheus.rule=Host(`collector-oracle-prometheus.obs.nsw.education`)",
          "traefik.http.routers.collector-oracle-prometheus.tls=false",
          "traefik.http.routers.collector-oracle-prometheus.entrypoints=https",
        ]

        check {
          type = "http"
          port = "port_prometheus"
          path = "/-/healthy"
          interval = "20s"
          timeout = "10s"
        }
      }

      template {
        data = <<EOH
global:
  external_labels:
    # nomad_job_name: {{ env "NOMAD_JOB_NAME" }}
    # nomad_task_name: {{ env "NOMAD_TASK_NAME" }}
    provenance: collector-oracle-prometheus
    env: prod

  scrape_interval: 120s

scrape_configs:
  # Job to scrape this instance of snmp exporter (ie. self)
  - job_name: 'prometheus-oracle'
    scheme: 'https'
    static_configs:
      - targets: ['collector-oracle-prometheus.obs.nsw.education']

  # Job to scrape metrics of the exporter itself
  - job_name: 'exporter-oracle'
    scheme: 'https'
    static_configs:
      - targets: [
#         'collector-oracle-exporter-blank.obs.nsw.education',
         'collector-oracle-exporter-mamid.obs.nsw.education',
      ]

#  - job_name: 'openmetrics_oracle'
#    consul_sd_configs:
#      - server: 'consul.service.dc-cir-un-prod.collectors.obs.nsw.education:8500'
#        datacenter: 'dc-cir-un-prod'
#        token: 'b68e1c4b-dac2-990b-1743-0d13056b56a5'
#        services: ['openmetrics_oracle']
#    relabel_configs:
#      - source_labels: [__meta_consul_metadata_cir_app_id]
#        target_label: cir_app_id
#      - source_labels: [__meta_consul_metadata_cir_app_id]
#        target_label: app_id
#      - source_labels: [__meta_consul_metadata_env]
#        target_label: env
#      - source_labels: [__meta_consul_service_metadata_domain]
#        target_label: domain
#      - source_labels: [__meta_consul_service_metadata_metrics_path]
#        target_label: __metrics_path__
#        regex: '(.+)'  # Do not perform the replace if there was no metrics path

remote_write:
  - name: mimir
    url: "https://mimir.obs.nsw.education/api/v1/push"
    headers: 
      X-Scope-OrgID: prod
    tls_config:
      insecure_skip_verify: true        

EOH
        destination = "local/prometheus.yaml"
      }
    }  // end-task "task-oracle-prometheus"



    # TASK exporter = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-oracle-exporter-blank" {
      driver = "docker"

      # Use the default oracle-in-a-box credentials, but we have proven out
      # the srvmtmzbx account (when created per standard process) works.
      env = {
        # DATA_SOURCE_NAME = "system/oracle@//pl0992obscol02.nsw.education:1521"
        # DATA_SOURCE_NAME = "system/oracle@//**************:1521"
        # DATA_SOURCE_NAME = "oracle://user:password@myhost:1521/service"
        # DATA_SOURCE_NAME = "system/oracle@//pl0475obscol06.nsw.education:1521"
        # DATA_SOURCE_NAME = "oracle://system:<EMAIL>:1521/service"
        DATA_SOURCE_NAME = "system/<EMAIL>:1521/XEPDB1"
      }

      config {
        image = var.image_exporter

        ports = ["port_exporter_blank"]

        args = [ 
          # "--custom.metrics=/etc/custom-metrics.toml",
          # "--log.level=debug",
          "--log.level=warn",
        ]

        volumes = [
          "local/custom-metrics.toml:/etc/custom-metrics.toml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }      

      }

      resources {
        cpu = 50
        memory = 50
        memory_max = 200
      }

      service {
        name = "collector-oracle-exporter-blank"
        port = "port_exporter_blank"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oracle-exporter-blank.rule=Host(`collector-oracle-exporter-blank.obs.nsw.education`)",
          "traefik.http.routers.collector-oracle-exporter-blank.tls=false",
          "traefik.http.routers.collector-oracle-exporter-blank.entrypoints=https",
        ]

        check {
          name     = "Oracle exporter healthcheck"
          port     = "port_exporter_blank"
          type     = "tcp"
          interval = "60s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }
      }

      # A custom-metrics (TOML format) can run queries and return them
      # in custom named key/value pairs, including customary # comments
      # for HELP and TYPE lines.
      template {
        data = <<EOH

[[metric]]
context = "test"

request = "SELECT 1 as value_1, 2 as value_2 FROM DUAL"

metricsdesc = { value_1 = "Custom key that always returns the value 1.", value_2 = "Custom key that always returns the value 2." }

metricstype = { value_1 = "counter" , value_2 = "counter" }


#[[metric]]
#context = "meta"
#
#request = "select to_char(startup_time,'YYYY-MM-DD HH24:MI:SS') "DB Startup Time" from sys.v_$instance;"
#
#metricsdesc = { startup_time = "Time that the database was started." }
#
#metricstype = { startup_time = "counter" }


EOH
        destination = "local/custom-metrics.toml"
      }

    } // end-task "task-oracle-exporter-blank"


    # TASK exporter for DB:  mamid  = = = = = = = = = = = = = = = = = = = = = = = = =
    task "task-oracle-exporter-mamid" {
      driver = "docker"

      # Use the default oracle-in-a-box credentials, but we have proven out
      # the srvmtmzbx account (when created per standard process) works.
      env = {
        
        DATA_SOURCE_NAME = "srvmtmzbx/Z4bb1xdu0992exad0200#@mamid.dbs.dev.det.nsw.edu.au:1521/mamid.dev.det.nsw.edu.au"
        

      }

      config {
        image = var.image_exporter

        ports = ["port_exporter_mamid"]

        args = [ 
          # "--custom.metrics=/etc/custom-metrics.toml",
        ]

        volumes = [
          "local/custom-metrics.toml:/etc/custom-metrics.toml",
        ]

        logging {
          type = "loki"
          config {
            loki-url = "https://loki.obs.nsw.education/loki/api/v1/push"
            loki-external-labels = "job=${NOMAD_JOB_ID},task=${NOMAD_TASK_NAME},env=prod"
          }
        }      

      }

      resources {
        cpu = 50
        memory = 50
        memory_max = 200
      }

      service {
        name = "collector-oracle-exporter-mamid"
        port = "port_exporter_mamid"

        tags = [
          "traefik.enable=true",
          "traefik.http.routers.collector-oracle-exporter-mamid.rule=Host(`collector-oracle-exporter-mamid.obs.nsw.education`)",
          "traefik.http.routers.collector-oracle-exporter-mamid.tls=false",
          "traefik.http.routers.collector-oracle-exporter-mamid.entrypoints=https",
        ]

        check {
          name     = "Oracle exporter healthcheck"
          port     = "port_exporter_mamid"
          type     = "tcp"
          interval = "60s"
          timeout  = "5s"
          check_restart {
            limit           = 3
            grace           = "60s"
            ignore_warnings = false
          }
        }
      }

      # A custom-metrics (TOML format) can run queries and return them
      # in custom named key/value pairs, including customary # comments
      # for HELP and TYPE lines.
      template {
        data = <<EOH


EOH
        destination = "local/custom-metrics.toml"
      }

    } // end-task "task-oracle-exporter-mamid"





  }  // end-group "collector-oracle" 

} // end-job "collector-oracle"


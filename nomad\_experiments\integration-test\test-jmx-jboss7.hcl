job "test-jmx-jboss7" {
  datacenters = ["dev"]
  type = "service"

  group "collector" {
    count = 1

    task "jboss" {
      driver = "docker"

      config {
        image = "tutum/jboss:as7"

        port_map {
          jboss = 8080
          jboss_alt = 9990
        }
      }

      env {
        "JBOSS_PASS" = "zabbix"
      }

      resources {
        network {
          port "jboss" {}
        }
      }

      service {
        name = "jboss7-integration-test"
        port = "jboss"
      }
    }
  }

}